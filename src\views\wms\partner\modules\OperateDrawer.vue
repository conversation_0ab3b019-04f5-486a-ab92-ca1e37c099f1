<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useCascaderAreaData } from '@vant/area-data';
import { partnerApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Partner | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

const areaData = useCascaderAreaData();

type Model = Pick<
  Api.Wms.Partner,
  | 'name'
  | 'type'
  | 'level'
  | 'summary'
  | 'contact'
  | 'phone'
  | 'email'
  | 'area'
  | 'address'
  | 'bankName'
  | 'bankAccount'
  | 'order'
  | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    type: [],
    level: 0,
    summary: '',
    contact: '',
    phone: '',
    email: '',
    area: null,
    address: '',
    bankName: '',
    bankAccount: '',
    order: 0,
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  type: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增记录',
    edit: '编辑记录'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();
    // request
    const { error } =
      props.operateType === 'edit' ? await partnerApi.save(model.value) : await partnerApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NTabs type="card" placement="left" animated>
          <NTabPane name="base" tab="基础信息">
            <NFormItem label="类型" path="type">
              <NSelect
                v-model:value="model.type"
                :options="useDict('number').items('PartnerType')"
                placeholder="请选择类型"
                multiple
                clearable
              />
            </NFormItem>
            <NFormItem label="名称" path="name">
              <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
            </NFormItem>
            <NFormItem label="评分" path="level">
              <NRate v-model:value="model.level" clearable />
            </NFormItem>
            <NFormItem label="省市区" path="area">
              <NCascader v-model:value="model.area" placeholder="请选择省市区" label-field="text" :options="areaData" />
            </NFormItem>
            <NFormItem label="详细地址" path="address">
              <NInput v-model:value="model.address" placeholder="请输入详细地址" type="textarea" clearable />
            </NFormItem>
            <NFormItem label="排序" path="order">
              <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
            </NFormItem>
            <NFormItem label="状态" path="status">
              <NSwitch v-model:value="model.status" />
            </NFormItem>
          </NTabPane>
          <NTabPane name="contact" tab="联系方式">
            <NFormItem label="联系人" path="contact">
              <NInput v-model:value="model.contact" placeholder="请输入联系人" clearable />
            </NFormItem>
            <NFormItem label="联系电话" path="phone">
              <NInput v-model:value="model.phone" placeholder="请输入联系电话" clearable />
            </NFormItem>
            <NFormItem label="邮箱" path="email">
              <NInput v-model:value="model.email" placeholder="请输入邮箱" clearable />
            </NFormItem>
          </NTabPane>
          <NTabPane name="more" tab="更多">
            <NFormItem label="开户行" path="bankName">
              <NInput v-model:value="model.bankName" placeholder="请输入开户行" clearable />
            </NFormItem>
            <NFormItem label="银行账号" path="bankAccount">
              <NInput v-model:value="model.bankAccount" placeholder="请输入银行账号" clearable />
            </NFormItem>
            <NFormItem label="备注" path="summary">
              <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
            </NFormItem>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

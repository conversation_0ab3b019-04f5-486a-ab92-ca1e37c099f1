import { request } from '@/service/request';

export const receiveSkuApi = {
  // 获取入库规格列表
  list: (params: Api.Wms.ReceiveSkuSearchParams) =>
    request<Api.Wms.ReceiveSkuList>({
      url: '/wms/receive-skus',
      params
    }),

  // 创建入库规格
  add: (data: Api.Wms.ReceiveSkuCreateParams) =>
    request<null>({
      url: '/wms/receive-skus',
      method: 'POST',
      data
    }),

  // 更新入库规格
  save: (data: Api.Wms.ReceiveSkuUpdateParams) =>
    request<null>({
      url: `/wms/receive-skus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除入库规格
  del: (id: number) =>
    request<null>({
      url: `/wms/receive-skus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除入库规格
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/receive-skus',
      method: 'DELETE',
      data: { ids }
    })
};

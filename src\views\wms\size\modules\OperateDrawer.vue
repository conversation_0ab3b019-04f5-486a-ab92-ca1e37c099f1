<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { printSizeApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.PrintSize | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<
  Api.Wms.PrintSize,
  'name' | 'paperSize' | 'orientation' | 'customSize' | 'summary' | 'order' | 'status'
>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    paperSize: 'A4',
    orientation: 'portrait',
    customSize: {
      width: 210,
      height: 297,
      unit: 'mm'
    },
    summary: '',
    order: 0,
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'paperSize' | 'orientation'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  paperSize: defaultRequiredRule,
  orientation: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增打印尺寸',
    edit: '编辑打印尺寸'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();
    // request
    const { error } =
      props.operateType === 'edit' ? await printSizeApi.save(model.value) : await printSizeApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="纸张尺寸" path="paperSize">
          <NSelect
            v-model:value="model.paperSize"
            :options="useDict().items('PaperSize')"
            placeholder="请选择纸张尺寸"
            clearable
          />
        </NFormItem>
        <NFormItem v-if="model.paperSize === 'custom'" label="自定义尺寸" path="customSize">
          <NSpace>
            <NInputNumber
              v-model:value="model.customSize.width"
              class="w-120px"
              placeholder="宽度"
              :min="0"
              :precision="0"
              clearable
            />
            <NInputNumber
              v-model:value="model.customSize.height"
              class="w-120px"
              placeholder="高度"
              :min="0"
              :precision="0"
              clearable
            />
            <NSelect
              v-model:value="model.customSize.unit"
              class="w-88px"
              :options="useDict().items('PrintUnit')"
              placeholder="单位"
              clearable
            />
          </NSpace>
        </NFormItem>
        <NFormItem label="纸张方向" path="orientation">
          <NRadioGroup v-model:value="model.orientation" name="orientation">
            <NSpace>
              <NRadio v-for="item in useDict().items('Orientation')" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadio>
            </NSpace>
          </NRadioGroup>
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="排序" path="order">
          <NInputNumber v-model:value="model.order" placeholder="请输入排序" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

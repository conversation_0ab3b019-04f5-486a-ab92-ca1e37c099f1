<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import emitter from '@/utils/mitt';
import Main from './modules/main/index.vue';
import Sku from './modules/sku/index.vue';

const check = ref<Api.Wms.Check | null>(null);

emitter.on('showCheckSku', (data: Api.Wms.Check | null) => {
  check.value = data;
});

onUnmounted(() => {
  emitter.off('showCheckSku');
});
</script>

<template>
  <div class="relative m-12px h-full">
    <Transition name="fade" mode="out-in">
      <Main v-show="!check" key="main" class="absolute inset-0" />
    </Transition>
    <Transition name="fade" mode="out-in">
      <Sku v-if="check" key="sku" class="absolute inset-0" :check="check" />
    </Transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

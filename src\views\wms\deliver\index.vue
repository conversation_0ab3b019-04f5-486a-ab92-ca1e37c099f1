<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import emitter from '@/utils/mitt';
import Main from './modules/main/index.vue';
import Sku from './modules/sku/index.vue';

const deliver = ref<Api.Wms.Deliver | null>(null);

emitter.on('showDeliverSku', (data: Api.Wms.Deliver | null) => {
  deliver.value = data;
});

onUnmounted(() => {
  emitter.off('showDeliverSku');
});
</script>

<template>
  <div class="relative m-12px h-full">
    <Transition name="fade" mode="out-in">
      <Main v-show="!deliver" key="main" class="absolute inset-0" />
    </Transition>
    <Transition name="fade" mode="out-in">
      <Sku v-if="deliver" key="sku" class="absolute inset-0" :deliver="deliver" />
    </Transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

<script setup lang="tsx">
import { onMounted, ref, watch } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState, TreeOption } from 'naive-ui';
import dayjs from 'dayjs';
import { areaApi, checkApi, checkSkuApi, metaApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { convertToTree } from '@/utils/common';
import emitter from '@/utils/mitt';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const props = defineProps<{
  check: Api.Wms.Check | null;
}>();

const appStore = useAppStore();

const areas = ref<Api.Wms.Area[]>([]);

const metaOptions = ref<TreeOption[]>([]);
const areaOptions = ref<TreeOption[]>([]);
onMounted(async () => {
  // 获取分类
  const { data: metaList, error: metaError } = await metaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!metaError) {
    metaOptions.value = convertToTree(metaList.records);
  }

  // 获取库位
  const { data: areaList, error: areaError } = await areaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!areaError) {
    areas.value = areaList.records;
    areaOptions.value = convertToTree(areaList.records);
  }
});

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: checkSkuApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,asc',
    _expand: 'check,item,sku',
    status: null,
    itemId: null,
    skuId: null,
    checkId: props.check?.id || 0
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
      disabled: row => row.status || (props.check !== null && props.check?.status > 2)
    },
    {
      key: 'itemId',
      title: '物料',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.item?.name}</span>
          {row.item?.code && row.item?.code !== row.item?.name && (
            <span class="text-12px text-gray"># {row.item?.code}</span>
          )}
        </div>
      )
    },
    {
      key: 'skuId',
      title: '规格',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.sku?.name}</span>
          {row.sku?.code && row.sku?.code !== row.sku?.name && (
            <span class="text-12px text-gray"># {row.sku?.code}</span>
          )}
        </div>
      )
    },
    {
      key: 'num',
      title: '库存量',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.num}</span>
          <span class="text-12px text-gray"># {row.sku?.unit}</span>
        </div>
      )
    },
    {
      key: 'remain',
      title: '实际库存',
      align: 'left',
      render: row => (
        <div class="flex flex-col text-ellipsis">
          <span>{row.remain}</span>
          <div class="flex items-center gap-x-4px text-12px">
            <span class="text-gray">#</span>
            {row.remain > row.num && (
              <div class="flex items-center gap-x-4px">
                <span class="text-success">盘盈</span>
                <span>{Math.abs(row.num - row.remain).toFixed(row.sku?.precision || 0)}</span>
              </div>
            )}
            {row.remain < row.num && (
              <div class="flex items-center gap-x-4px">
                <span class="text-error">盘亏</span>
                <span>{Math.abs(row.num - row.remain).toFixed(row.sku?.precision || 0)}</span>
              </div>
            )}
            <span class="text-gray">{row.sku?.unit}</span>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => (row.status ? <NTag type="success">已盘点</NTag> : <NTag>未盘点</NTag>)
    },
    {
      key: 'createdAt',
      title: '创建时间',
      align: 'left',
      sorter: true,
      render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            {row.status || (props.check !== null && props.check?.status > 3) ? '查看' : '编辑'}
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton
                  type="error"
                  text
                  size="small"
                  disabled={row.status || (props.check !== null && props.check?.status > 2)}
                >
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await checkSkuApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await checkSkuApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order,id',
      _order: 'desc,asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}

const checkId = ref(props.check?.id || 0);

async function handleItemChange(_: any, option?: any) {
  emitter.emit('showCheckSku', option);
}

watch(
  () => checkId.value,
  async nval => {
    updateSearchParams({
      checkId: nval
    });
    await getData();
  }
);
</script>

<template>
  <div class="h-full flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :disabled-add="check !== null && check.status > 2"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        >
          <template #suffix>
            <RemoteSelect
              v-model:value="checkId"
              class="w-200px"
              label-field="code"
              value-field="id"
              :options="[check]"
              :api-fn="checkApi.list"
              placeholder="请选择盘点单"
              :clearable="false"
              @update:value="handleItemChange"
            />
            <NButton @click="emitter.emit('showCheckSku', null)">
              <template #icon>
                <icon-ph-arrow-left />
              </template>
            </NButton>
          </template>
        </TableHeaderOperation>
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :meta-options="metaOptions"
        :area-options="areaOptions"
        :check="check"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>

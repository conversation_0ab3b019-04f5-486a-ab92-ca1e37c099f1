<script setup lang="ts">
import { onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { transferApi, transferSkuApi } from '@/service/api/wms';
import { useDict } from '@/hooks/business/dict';
import QRCode from '@/components/custom/qr-code.vue';

const props = defineProps<{
  id: number;
}>();

const data = ref<Api.Wms.Transfer | null>(null);

async function getData() {
  const { data: transfer, error } = await transferApi.get(props.id, {
    _expand: 'partner'
  });
  if (!error) {
    data.value = transfer;
  }
}

const skuData = ref<Api.Wms.TransferSku[]>([]);

async function getSkuData() {
  const { data: skuList, error } = await transferSkuApi.list({
    _page: 1,
    _limit: 9999,
    _sort: 'order,id',
    _order: 'desc,asc',
    _expand: 'item,sku',
    transferId: props.id
  });
  if (!error) {
    skuData.value = skuList.records;
  }
}

onMounted(async () => {
  await getData();
  await getSkuData();
});
</script>

<template>
  <div v-if="data" class="print">
    <div class="flex items-center justify-between">
      <div class="text-2xl font-bold">调拨单：{{ data.code }}</div>
      <div class="h-100px">
        <QRCode class="h-full" :value="data.code" />
      </div>
    </div>
    <div class="m-t-24px flex flex-wrap items-center gap-y-16px">
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">调拨类型：</div>
        <div>{{ useDict('number').item('TransferType', data.type)?.label }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">调拨状态：</div>
        <div>{{ useDict('number').item('OrderStatus', data.status)?.label }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">创建人：</div>
        <div>{{ data.createdBy || '-' }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">创建时间：</div>
        <div>{{ dayjs(data.createdAt).format('YYYY-MM-DD HH:mm') }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">操作人：</div>
        <div>{{ data.updatedBy || '-' }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">操作时间：</div>
        <div>{{ dayjs(data.updatedAt).format('YYYY-MM-DD HH:mm') }}</div>
      </div>
      <div class="w-full flex">
        <div class="w-100px font-bold">备注：</div>
        <div>{{ data.summary || '-' }}</div>
      </div>
    </div>
    <div class="m-t-24px">
      <NTable>
        <thead>
          <tr>
            <th>序号</th>
            <th>物料</th>
            <th>规格</th>
            <th>数量</th>
            <th>单位</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in skuData" :key="item.id">
            <td>{{ index + 1 }}</td>
            <td>{{ item.item?.name }}</td>
            <td>{{ item.sku?.name }}</td>
            <td>{{ item.num }}</td>
            <td>{{ item.sku?.unit }}</td>
          </tr>
        </tbody>
      </NTable>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

<script setup lang="ts">
import { ref, watch } from 'vue';

defineOptions({
  name: 'SelectBool',
  inheritAttrs: false
});

defineProps<{
  options: any[] | undefined;
}>();

const value = defineModel<boolean | number | null | undefined>('value', { required: true });

const selectValue = ref();

// 监听 value 变化，更新 selectValue
watch(
  () => value.value,
  val => {
    switch (val) {
      case true:
        selectValue.value = 'true';
        break;
      case false:
        selectValue.value = 'false';
        break;
      default:
        selectValue.value = undefined;
    }
  },
  { immediate: true }
);

// 监听 selectValue 变化，更新 value
watch(
  () => selectValue.value,
  val => {
    switch (val) {
      case 'true':
        value.value = true;
        break;
      case 'false':
        value.value = false;
        break;
      default:
        value.value = undefined;
    }
  }
);
</script>

<template>
  <NSelect v-model:value="selectValue" :options="options" v-bind="$attrs" />
</template>

<style scoped></style>

<script setup lang="tsx">
import { useRouter } from 'vue-router';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import dayjs from 'dayjs';
import { receiveApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import emitter from '@/utils/mitt';
import { fastPrint } from '@/utils/common';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const router = useRouter();
const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: receiveApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'id',
    _order: 'desc',
    _expand: 'partner',
    status: null,
    code: null,
    type: null,
    relatedNo: null,
    partnerId: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48,
      disabled: row => row.status > 2
    },
    {
      key: 'code',
      title: '单号',
      align: 'left'
    },
    {
      key: 'type',
      title: '类型',
      align: 'left',
      render: row => {
        if (row.type === null) return null;

        const option = useDict('number').item('ReceiveType', row.type);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'relatedNo',
      title: '关联单号',
      align: 'left'
    },
    {
      key: 'partnerId',
      title: '供应商',
      align: 'left',
      render: row => row.partner?.name
    },
    {
      key: 'status',
      title: '状态',
      align: 'left',
      render: row => {
        if (row.status === null) return null;

        const option = useDict('number').item('OrderStatus', row.status);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'createdAt',
      title: '创建时间',
      align: 'left',
      sorter: true,
      render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 120,
      render: row => (
        <div class="flex flex-center flex-wrap gap-12px">
          <NButton
            type="success"
            text
            size="small"
            disabled={row.status === 5}
            onClick={() => emitter.emit('showReceiveSku', row)}
          >
            物料
          </NButton>
          <NButton
            type="warning"
            text
            size="small"
            disabled={![3, 4].includes(row.status)}
            onClick={() => fastPrint({ url: createPrintUrl(row.id) })}
          >
            打印
          </NButton>
          <NButton type="primary" text size="small" disabled={row.status > 3} onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small" disabled={row.status > 2}>
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await receiveApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await receiveApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'id',
      _order: 'desc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}

// 创建打印地址
function createPrintUrl(id: number) {
  return router.resolve({
    path: '/wms/print',
    query: { template: 'receive', id }
  }).href;
}
</script>

<template>
  <div class="h-full flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { type PrintAreaOption, VuePrintNext } from 'vue-print-next';
import { printSizeApi } from '@/service/api/wms';
import { useDict } from '@/hooks/business/dict';

const props = defineProps<{
  data: Record<string, any>;
  options?: Record<string, any>;
}>();

const router = useRouter();

const url = computed(
  () =>
    router.resolve({
      path: '/wms/print',
      query: props.data
    }).href
);

const printSizes = ref<Api.Wms.PrintSize[]>([]);
const printSize = ref<number | null>(null);

async function getData() {
  const { data, error } = await printSizeApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!error) {
    printSizes.value = data.records;
  }
}

onMounted(async () => {
  await getData();
});

const visible = defineModel<boolean>('visible', {
  default: false
});

const model = ref(createDefaultModel());

function createDefaultModel() {
  return {
    preview: true,
    previewTools: {
      zoom: true,
      theme: true,
      fullscreen: true
    },
    darkMode: false,
    windowMode: false,
    paperSize: 'A4',
    orientation: 'portrait',
    defaultScale: 1,
    customSize: {
      width: 210,
      height: 297,
      unit: 'mm'
    },
    url: url.value,
    previewOpenCallback() {
      visible.value = false;
    }
  };
}

function handleInitModel() {
  model.value = createDefaultModel();

  Object.assign(model.value, props.options);
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
  } else {
    printSize.value = null;
  }
});

function handlePrint() {
  // eslint-disable-next-line no-new
  new VuePrintNext(model.value as PrintAreaOption);
}

watch(printSize, () => {
  if (printSize.value) {
    const size = printSizes.value.find(item => item.id === printSize.value);
    if (size) {
      Object.assign(model.value, size);
    }
  } else {
    handleInitModel();
  }
});
</script>

<template>
  <NModal v-model:show="visible" title="打印格式" preset="card" class="w-500px">
    <NForm :label-width="100" label-placement="left">
      <NFormItem label="纸张尺寸" path="paperSize">
        <NSelect v-model:value="model.paperSize" :options="useDict().items('PaperSize')" :disabled="!!printSize" />
      </NFormItem>
      <NFormItem v-if="model.customSize && model.paperSize === 'custom'" label="自定义尺寸" path="customSize">
        <NSpace>
          <NInputNumber
            v-model:value="model.customSize.width"
            class="w-120px"
            placeholder="宽度"
            :min="0"
            :precision="0"
            :disabled="!!printSize"
          />
          <NInputNumber
            v-model:value="model.customSize.height"
            class="w-120px"
            placeholder="高度"
            :min="0"
            :precision="0"
            :disabled="!!printSize"
          />
          <NSelect
            v-model:value="model.customSize.unit"
            class="w-88px"
            :options="useDict().items('PrintUnit')"
            placeholder="单位"
            :disabled="!!printSize"
          />
        </NSpace>
      </NFormItem>
      <NFormItem label="纸张方向" path="orientation">
        <NRadioGroup v-model:value="model.orientation" name="orientation" :disabled="!!printSize">
          <NSpace>
            <NRadio v-for="item in useDict().items('Orientation')" :key="item.value" :value="item.value">
              {{ item.label }}
            </NRadio>
          </NSpace>
        </NRadioGroup>
      </NFormItem>
      <NFormItem label="更多设置" path="more">
        <NSpace>
          <NCheckbox v-model:checked="model.darkMode">深色</NCheckbox>
          <NCheckbox v-model:checked="model.windowMode">窗口</NCheckbox>
        </NSpace>
      </NFormItem>
      <NFormItem label="预览缩放" path="defaultScale">
        <NSlider v-model:value="model.defaultScale" :min="0.5" :max="2" :step="0.1" />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace justify="space-between" :size="16">
        <NSelect
          v-model:value="printSize"
          class="w-180px"
          label-field="name"
          value-field="id"
          :options="printSizes"
          placeholder="预置格式"
          clearable
        />
        <NSpace justify="end" :size="16">
          <NButton @click="visible = false">取消</NButton>
          <NButton type="primary" @click="handlePrint">确定</NButton>
        </NSpace>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>

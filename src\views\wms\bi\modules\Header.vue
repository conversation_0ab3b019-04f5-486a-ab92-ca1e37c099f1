<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';

defineProps<{
  title: string;
}>();

// 全屏状态
const isFullscreen = ref(false);

// 切换全屏
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    // 进入全屏
    document.documentElement.requestFullscreen().then(() => {
      isFullscreen.value = true;
    });
  } else {
    // 退出全屏
    document.exitFullscreen().then(() => {
      isFullscreen.value = false;
    });
  }
}

// 监听全屏状态变化
function handleFullscreenChange() {
  isFullscreen.value = Boolean(document.fullscreenElement);
}

onMounted(() => {
  // 添加全屏状态变化监听
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onBeforeUnmount(() => {
  // 移除全屏状态变化监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
});

// 高德秘钥
const key = import.meta.env.VITE_AMAP_SERVICE_KEY;

// 星期
const week = ['日', '一', '二', '三', '四', '五', '六'];

const timestamp = ref();

const city = ref();
const today = ref();
const casts = ref();

async function getWeather() {
  const cache = localStorage.getItem('weather-cache');
  if (cache) {
    const { data, expires } = JSON.parse(cache);
    if (expires > Date.now()) {
      city.value = data.city;
      today.value = data.today;
      casts.value = data.casts;
      return;
    }
  }
  // 获取城市
  const res = await axios.request({
    url: 'https://restapi.amap.com/v3/ip',
    params: { key }
  });
  if (res.status === 200) {
    city.value = res.data.area || res.data.city;
    // 今天
    const todayRes = await axios.request({
      url: 'https://restapi.amap.com/v3/weather/weatherInfo',
      params: {
        key,
        city: city.value,
        extensions: 'base'
      }
    });
    today.value = todayRes.data.lives[0];

    // 未来4天
    const castsRes = await axios.request({
      url: 'https://restapi.amap.com/v3/weather/weatherInfo',
      params: {
        key,
        city: city.value,
        extensions: 'all'
      }
    });
    casts.value = castsRes.data.forecasts[0].casts;
    casts.value.shift();

    // 缓存
    localStorage.setItem(
      'weather-cache',
      JSON.stringify({
        data: {
          city: city.value,
          today: today.value,
          casts: casts.value
        },
        expires: Date.now() + 60 * 60 * 1000
      })
    );
  }
}

const weatherTimer = ref();
const dateTimer = ref();

onMounted(async () => {
  timestamp.value = dayjs();
  dateTimer.value = setInterval(() => (timestamp.value = dayjs()), 1000);

  await getWeather();
  weatherTimer.value = setInterval(() => getWeather(), 3600 * 6 * 1000);
});

onBeforeUnmount(() => {
  clearInterval(weatherTimer.value);
  clearInterval(dateTimer.value);
});
</script>

<template>
  <div class="header flex items-center justify-between px-25rem">
    <div class="w-30% pt-30rem">
      <div v-if="today" class="flex items-center">
        <span class="area mr-10rem">{{ today.city }}</span>
        <span>/</span>
        <span class="temp mx-10rem">{{ today.weather }} {{ today.temperature }}℃</span>
        <span>/</span>
        <span class="wind mx-10rem">{{ today.winddirection }}风{{ today.windpower }}</span>
      </div>
    </div>
    <div class="title w-40% text-center">
      <span @dblclick="toggleFullscreen">{{ title }}</span>
    </div>
    <div class="w-30% pt-30rem">
      <div class="flex items-center justify-end">
        <template v-if="timestamp">
          <span class="date mr-10rem">{{ timestamp.format('YYYY年MM月DD日') }}</span>
          <span class="week mr-10rem">星期{{ week[timestamp.day()] }}</span>
          <span class="time">{{ timestamp.format('HH:mm:ss') }}</span>
        </template>
        <icon-ph-arrow-circle-left
          v-if="!isFullscreen"
          class="ml-10rem cursor-pointer text-24rem opacity-50 hover:opacity-100"
          @click="$router.back"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.header {
  position: relative;
  width: 100%;
  height: 63rem;
  background: url(@/assets/imgs/bi/<EMAIL>) no-repeat center bottom;
  background-size: 100% 100%;
  font-size: 16rem;
  letter-spacing: 2rem;
  color: rgba(57, 252, 255, 0.8);

  &::after {
    position: absolute;
    left: 50%;
    bottom: -40rem;
    content: '';
    width: 996rem;
    height: 80rem;
    margin-left: -498rem;
    background: url(@/assets/imgs/bi/<EMAIL>) no-repeat center;
  }

  .title {
    position: relative;
    z-index: 2;
    font-family: 'AlimamaShuHeiTi';
    font-size: 32rem;
    line-height: 63rem;
    font-weight: bold;
    letter-spacing: 10rem;
    color: #39fcff;
    text-shadow: 0 0 15rem #009caf;

    span {
      -webkit-text-stroke: 1rem #001067;
      user-select: none;
    }
  }
}
</style>

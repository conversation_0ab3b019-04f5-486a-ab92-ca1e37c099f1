<script setup lang="ts">
import { userApi } from '@/service/api/system';
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.System.LogSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
  emit('search');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="请求用户" path="userId">
              <RemoteSelect
                v-model:value="model.userId"
                label-field="username"
                value-field="userId"
                :api-fn="userApi.list"
                placeholder="请选择用户"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="请求方法" path="method">
              <NSelect v-model:value="model.method" :options="useDict().items('HttpMethod')" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态码" path="code">
              <NInputNumber
                v-model:value="model.code"
                class="w-full"
                placeholder="请输入状态码"
                :show-button="false"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="客户端IP" path="clientIp">
              <NInput v-model:value="model.clientIp" placeholder="请输入客户端IP" clearable />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>

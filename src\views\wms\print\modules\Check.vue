<script setup lang="ts">
import { onMounted, ref } from 'vue';
import dayjs from 'dayjs';
import { areaApi, checkApi, checkSkuApi } from '@/service/api/wms';
import { useDict } from '@/hooks/business/dict';
import { showArea } from '@/utils/common';
import QRCode from '@/components/custom/qr-code.vue';

const props = defineProps<{
  id: number;
}>();

const data = ref<Api.Wms.Check | null>(null);

async function getData() {
  const { data: check, error } = await checkApi.get(props.id, {
    _expand: 'partner'
  });
  if (!error) {
    data.value = check;
  }
}

const skuData = ref<Api.Wms.CheckSku[]>([]);

async function getSkuData() {
  const { data: skuList, error } = await checkSkuApi.list({
    _page: 1,
    _limit: 9999,
    _sort: 'order,id',
    _order: 'desc,asc',
    _expand: 'item,sku',
    checkId: props.id
  });
  if (!error) {
    skuData.value = skuList.records;
  }
}

const areaData = ref<Api.Wms.Area[]>([]);

async function getAreaData() {
  // 获取库位
  const { data: areaList, error: areaError } = await areaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!areaError) {
    areaData.value = areaList.records;
  }
}

onMounted(async () => {
  await getData();
  await getSkuData();
  await getAreaData();
});
</script>

<template>
  <div v-if="data" class="print">
    <div class="flex items-center justify-between">
      <div class="text-2xl font-bold">盘点单：{{ data.code }}</div>
      <div class="h-100px">
        <QRCode class="h-full" :value="data.code" />
      </div>
    </div>
    <div class="m-t-24px flex flex-wrap items-center gap-y-16px">
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">盘点类型：</div>
        <div>{{ useDict('number').item('CheckType', data.type)?.label }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">盘点状态：</div>
        <div>{{ useDict('number').item('OrderStatus', data.status)?.label }}</div>
      </div>
      <div class="w-full flex">
        <div class="w-100px font-bold">目标库位：</div>
        <div>{{ showArea(data.areaPath, areaData, false) }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">创建人：</div>
        <div>{{ data.createdBy || '-' }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">创建时间：</div>
        <div>{{ dayjs(data.createdAt).format('YYYY-MM-DD HH:mm') }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">操作人：</div>
        <div>{{ data.updatedBy || '-' }}</div>
      </div>
      <div class="w-1/2 flex">
        <div class="w-100px font-bold">操作时间：</div>
        <div>{{ dayjs(data.updatedAt).format('YYYY-MM-DD HH:mm') }}</div>
      </div>
      <div class="w-full flex">
        <div class="w-100px font-bold">备注：</div>
        <div>{{ data.summary || '-' }}</div>
      </div>
    </div>
    <div class="m-t-24px">
      <NTable>
        <thead>
          <tr>
            <th>序号</th>
            <th>物料</th>
            <th>规格</th>
            <th>单位</th>
            <th>库存量</th>
            <th>实际库存</th>
            <th>盈亏</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in skuData" :key="item.id">
            <td>{{ index + 1 }}</td>
            <td>{{ item.item?.name }}</td>
            <td>{{ item.sku?.name }}</td>
            <td>{{ item.sku?.unit }}</td>
            <td>{{ item.num }}</td>
            <td>{{ item.remain }}</td>
            <td>{{ Number(item.num - item.remain).toFixed(item.sku?.precision || 0) }}</td>
          </tr>
        </tbody>
      </NTable>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

import { request } from '@/service/request';

export const printSizeApi = {
  // 获取打印尺寸列表
  list: (params: Api.Wms.PrintSizeSearchParams) =>
    request<Api.Wms.PrintSizeList>({
      url: '/wms/print-sizes',
      params
    }),

  // 创建打印尺寸
  add: (data: Api.Wms.PrintSizeCreateParams) =>
    request<null>({
      url: '/wms/print-sizes',
      method: 'POST',
      data
    }),

  // 更新打印尺寸
  save: (data: Api.Wms.PrintSizeUpdateParams) =>
    request<null>({
      url: `/wms/print-sizes/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除打印尺寸
  del: (id: number) =>
    request<null>({
      url: `/wms/print-sizes/${id}`,
      method: 'DELETE'
    }),

  // 批量删除打印尺寸
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/print-sizes',
      method: 'DELETE',
      data: { ids }
    })
};

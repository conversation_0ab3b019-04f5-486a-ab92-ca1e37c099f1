<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { tenantApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.System.Tenant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.System.Tenant, 'name' | 'code' | 'summary' | 'expiredAt' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    name: '',
    code: '',
    summary: '',
    expiredAt: '',
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code' | 'expiredAt'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule,
  expiredAt: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增租户',
    edit: '编辑租户'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();
    // request
    const { error } =
      props.operateType === 'edit' ? await tenantApi.save(model.value) : await tenantApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

// 过期时间
const expiredAt = computed({
  get: () => (model.value.expiredAt ? dayjs(model.value.expiredAt).valueOf() : null),
  set: val => {
    model.value.expiredAt = val ? dayjs(val).toISOString() : '';
  }
});

// 快捷时间
const shortcuts: Record<string, number> = {
  '1年': dayjs().add(1, 'year').valueOf(),
  '2年': dayjs().add(2, 'year').valueOf(),
  '3年': dayjs().add(3, 'year').valueOf()
};
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入租户名称" clearable />
        </NFormItem>
        <NFormItem label="编码" path="code">
          <NInput v-model:value="model.code" placeholder="请输入租户编码" clearable />
        </NFormItem>
        <NFormItem label="描述" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入租户描述" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="过期时间" path="expiredAt">
          <NDatePicker v-model:value="expiredAt" type="date" :shortcuts="shortcuts" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

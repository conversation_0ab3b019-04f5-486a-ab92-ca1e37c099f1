<script setup lang="tsx">
import type { Ref } from 'vue';
import { ref, watch } from 'vue';
import { NButton, NPopconfirm, NTag } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { jsonClone } from '@sa/utils';
import { areaApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { useDict } from '@/hooks/business/dict';
import { convertToTree } from '@/utils/common';
import PrintSetting from '@/components/common/print-setting.vue';
import SwitchStatus from '@/components/custom/switch-status.vue';
import OperateDrawer from './modules/OperateDrawer.vue';

const appStore = useAppStore();

const { columns, columnChecks, data, getData, getDataByPage, loading, pagination, updateSearchParams } = useTable({
  apiFn: areaApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'asc,asc'
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '名称',
      align: 'left'
    },
    {
      key: 'code',
      title: '编码',
      align: 'left'
    },
    {
      key: 'type',
      title: '类型',
      align: 'left',
      render: row => {
        if (row.type === null) return null;

        const option = useDict('number').item('AreaType', row.type);
        if (!option) return null;
        return <NTag type={option.type}>{option.label}</NTag>;
      }
    },
    {
      key: 'summary',
      title: '描述',
      align: 'left',
      minWidth: 200,
      ellipsis: true
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => areaApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 120,
      render: row => (
        <div class="flex flex-center flex-wrap gap-12px">
          <NButton type="success" text size="small" disabled={row.type === 3} onClick={() => addChild(row.id)}>
            新增
          </NButton>
          <NButton type="warning" text size="small" onClick={() => print(row.id)}>
            打印
          </NButton>
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

// 树形数据
const treeData = ref<Api.Wms.Area[]>([]);
watch(data, nval => {
  treeData.value = nval.length > 0 ? convertToTree(nval) : [];
});

async function handleBatchDelete() {
  const { error } = await areaApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await areaApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'id',
      _order: 'desc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}

function addChild(id: number) {
  (operateType as Ref<NaiveUI.TableOperateType | 'addChild'>).value = 'addChild';
  const findItem = data.value.find(item => item.id === id) || null;
  editingData.value = jsonClone(findItem);

  drawerVisible.value = true;
}

// 打印
const printSettingVisible = ref(false);
const printData = ref({
  template: 'area',
  id: 0
});

function print(id: number) {
  printData.value.id = id;
  printSettingVisible.value = true;
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="treeData"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="false"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
      <PrintSetting v-model:visible="printSettingVisible" :data="printData" />
    </NCard>
  </div>
</template>

<style scoped></style>

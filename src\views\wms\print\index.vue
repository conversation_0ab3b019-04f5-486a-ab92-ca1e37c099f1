<script setup lang="ts">
import { useRoute } from 'vue-router';
import Receive from './modules/Receive.vue';
import Deliver from './modules/Deliver.vue';
import Transfer from './modules/Transfer.vue';
import Check from './modules/Check.vue';
import Area from './modules/Area.vue';
import Sku from './modules/Sku.vue';

const route = useRoute();

const { template, id } = route.query;
</script>

<template>
  <div class="bg-white p-16px">
    <Receive v-if="template === 'receive'" :id="Number(id)" />
    <Deliver v-if="template === 'deliver'" :id="Number(id)" />
    <Transfer v-if="template === 'transfer'" :id="Number(id)" />
    <Check v-if="template === 'check'" :id="Number(id)" />
    <Area v-if="template === 'area'" :id="Number(id)" />
    <Sku v-if="template === 'sku'" :id="Number(id)" />
  </div>
</template>

<style scoped lang="scss">
@media print {
  @page {
    margin: 0;
  }
  body {
    margin: 0;
  }
  .no-print {
    display: none;
  }
}
</style>

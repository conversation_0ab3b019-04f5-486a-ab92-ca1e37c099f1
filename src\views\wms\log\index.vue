<script setup lang="tsx">
import { N<PERSON><PERSON><PERSON>, NPopconfirm } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import dayjs from 'dayjs';
import { logApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import SearchBar from './modules/SearchBar.vue';

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: logApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'id',
    _order: 'desc',
    _expand: 'item,sku',
    itemId: null,
    skuId: null,
    type: null,
    relatedNo: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'itemId',
      title: '物料',
      align: 'left',
      render: row => (
        <div class="flex items-center gap-x-12px text-ellipsis">
          <span>{row.item?.name}</span>
          {row.item?.code && row.item?.code !== row.item?.name && <span class="text-gray">#{row.item?.code}</span>}
        </div>
      )
    },
    {
      key: 'skuId',
      title: '规格',
      align: 'left',
      render: row => (
        <div class="flex items-center gap-x-8px text-ellipsis">
          <span>{row.sku?.name}</span>
          {row.sku?.code && row.sku?.code !== row.sku?.name && <span class="text-gray">#{row.sku?.code}</span>}
        </div>
      )
    },
    {
      key: 'summary',
      title: '备注',
      align: 'left',
      minWidth: 200,
      ellipsis: true
    },
    {
      key: 'createdAt',
      title: '创建时间',
      align: 'left',
      sorter: true,
      render: row => dayjs(row.createdAt).format('YYYY-MM-DD HH:mm')
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { checkedRowKeys, onBatchDeleted, onDeleted } = useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await logApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await logApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'id',
      _order: 'desc'
    });
  }
  await getData();
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          disabled-add
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
    </NCard>
  </div>
</template>

<style scoped></style>

import { request } from '@/service/request';

export const packApi = {
  // 获取包装列表
  list: (params: Api.Wms.PackSearchParams) =>
    request<Api.Wms.PackList>({
      url: '/wms/packs',
      params
    }),

  // 创建包装
  add: (data: Api.Wms.PackCreateParams) =>
    request<null>({
      url: '/wms/packs',
      method: 'POST',
      data
    }),

  // 更新包装
  save: (data: Api.Wms.PackUpdateParams) =>
    request<null>({
      url: `/wms/packs/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除包装
  del: (id: number) =>
    request<null>({
      url: `/wms/packs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除包装
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/packs',
      method: 'DELETE',
      data: { ids }
    })
};

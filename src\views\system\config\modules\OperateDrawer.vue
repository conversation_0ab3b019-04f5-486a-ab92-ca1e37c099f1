<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { nanoid } from 'nanoid';
import { configApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.System.ConfigParam | null;
  /** the config */
  config: Api.System.Config | null | undefined;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.System.ConfigParam, 'id' | 'name' | 'code' | 'value' | 'summary' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: nanoid(8),
    name: '',
    code: '',
    value: '',
    summary: '',
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'name' | 'code'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  name: defaultRequiredRule,
  code: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增参数',
    edit: '编辑参数'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();

    const params =
      props.operateType === 'add'
        ? (props.config?.params || []).concat([model.value])
        : props.config?.params?.map(param => {
            return param.id === model.value.id ? model.value : param;
          });
    // request
    const { error } = await configApi.save({ ...props.config, params });

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="名称" path="name">
          <NInput v-model:value="model.name" placeholder="请输入名称" clearable />
        </NFormItem>
        <NFormItem label="编码" path="code">
          <NInput v-model:value="model.code" placeholder="请输入编码" clearable />
        </NFormItem>
        <NFormItem label="值" path="value">
          <NInput v-model:value="model.value" placeholder="请输入值" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="描述" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入描述" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped lang="scss">
:deep(.n-select) {
  .n-base-selection {
    .n-base-selection-label {
      .n-base-selection-input {
        padding-left: 3px;
      }
    }
  }
}
</style>

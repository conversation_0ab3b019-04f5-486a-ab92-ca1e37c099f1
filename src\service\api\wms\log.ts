import { request } from '@/service/request';

export const logApi = {
  // 获取日志列表
  list: (params: Api.Wms.LogSearchParams) =>
    request<Api.Wms.LogList>({
      url: '/wms/logs',
      params
    }),

  // 创建日志
  add: (data: Api.Wms.LogCreateParams) =>
    request<null>({
      url: '/wms/logs',
      method: 'POST',
      data
    }),

  // 更新日志
  save: (data: Api.Wms.LogUpdateParams) =>
    request<null>({
      url: `/wms/logs/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除日志
  del: (id: number) =>
    request<null>({
      url: `/wms/logs/${id}`,
      method: 'DELETE'
    }),

  // 批量删除日志
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/logs',
      method: 'DELETE',
      data: { ids }
    })
};

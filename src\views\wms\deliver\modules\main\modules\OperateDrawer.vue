<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { deliverApi, partnerApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { suggestCode } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Deliver | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.Wms.Deliver, 'code' | 'type' | 'summary' | 'status' | 'relatedNo' | 'partnerId'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    code: suggestCode('CK'),
    type: 1,
    summary: '',
    status: 2,
    relatedNo: '',
    partnerId: null
  };
}

type RuleKey = Extract<keyof Model, 'code' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  code: defaultRequiredRule,
  type: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增出库单',
    edit: '编辑出库单'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit(fn?: () => void) {
  loading.value = true;
  try {
    // 执行额外函数
    if (fn) {
      fn();
    }

    await validate();
    // request
    const { error } =
      props.operateType === 'edit' ? await deliverApi.save(model.value) : await deliverApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="单号" path="code">
          <NInput v-model:value="model.code" placeholder="请输入单号" disabled clearable />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NSelect
            v-model:value="model.type"
            :options="useDict('number').items('DeliverType')"
            placeholder="请选择类型"
            :disabled="model.status === 3"
            clearable
          />
        </NFormItem>
        <NFormItem label="客户" path="partnerId">
          <RemoteSelect
            v-model:value="model.partnerId"
            label-field="name"
            value-field="id"
            :options="[rowData?.partner]"
            :api-fn="partnerApi.list"
            :filter="{ type: 2 }"
            placeholder="请选择客户"
            :disabled="model.status === 3"
            clearable
          />
        </NFormItem>
        <NFormItem label="关联单号" path="relatedNo">
          <NInput
            v-model:value="model.relatedNo"
            placeholder="请输入关联单号"
            :disabled="model.status === 3"
            clearable
          />
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton v-if="model.status === 2" :loading="loading" @click="handleSubmit(() => (model.status = 1))">
            暂存草稿
          </NButton>
          <NButton
            v-if="model.status === 1 || operateType === 'add'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 2))"
          >
            发布待审
          </NButton>
          <NButton
            v-if="model.status === 2 && operateType === 'edit'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 3))"
          >
            确认审核
          </NButton>
          <NButton
            v-if="model.status === 3 && operateType === 'edit'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 4))"
          >
            确认完成
          </NButton>
          <NButton
            v-if="model.status === 3 && operateType === 'edit'"
            type="error"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 5))"
          >
            作废
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

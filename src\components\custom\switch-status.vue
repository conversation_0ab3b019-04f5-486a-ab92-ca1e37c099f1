<script setup lang="ts">
import { ref } from 'vue';

defineOptions({
  name: 'SwitchStatus'
});

const props = defineProps<{
  apiFn: (value: boolean) => Promise<{ data: any; error: Error | null }>;
}>();

const value = defineModel<boolean>('value');

const loading = ref(false);

const handleUpdate = async (newValue: boolean) => {
  loading.value = true;
  try {
    const { error } = await props.apiFn(newValue);
    if (!error) {
      window.$message?.success('状态更新成功');
      value.value = newValue;
    }
  } catch {
    window.$message?.error('状态更新失败');
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <NSwitch :value="value" :loading="loading" :rubber-band="false" @update:value="handleUpdate" />
</template>

<style scoped></style>

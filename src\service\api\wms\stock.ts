import { request } from '@/service/request';

export const stockApi = {
  // 获取库存列表
  list: (params: Api.Wms.StockSearchParams) =>
    request<Api.Wms.StockList>({
      url: '/wms/stocks',
      params
    }),

  // 创建库存
  add: (data: Api.Wms.StockCreateParams) =>
    request<null>({
      url: '/wms/stocks',
      method: 'POST',
      data
    }),

  // 更新库存
  save: (data: Api.Wms.StockUpdateParams) =>
    request<null>({
      url: `/wms/stocks/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除库存
  del: (id: number) =>
    request<null>({
      url: `/wms/stocks/${id}`,
      method: 'DELETE'
    }),

  // 批量删除库存
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/stocks',
      method: 'DELETE',
      data: { ids }
    })
};

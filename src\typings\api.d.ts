/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** option */
    type Option = {
      label: string;
      value: string;
    };

    /**
     * gender
     *
     * - 0: "unknown"
     * - 1: "male"
     * - 2: "female"
     */
    type Gender = 0 | 1 | 2;

    /** http method */
    type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

    /** upload file */
    type UploadFile = {
      id: string;
      name: string;
      size: number;
      type: string;
      url: string;
    };

    /** common record */
    type Record<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createdBy: string;
      /** record create time */
      createdAt: string;
      /** record updater */
      updatedBy: string;
      /** record update time */
      updatedAt: string;
    } & T;

    /** common params of paginating query list data */
    interface PaginatingRecord<T = any> {
      records: T[];
      total: number;
    }

    /** common params of paginating */
    interface PaginatingParams {
      /** current page number */
      _page: number;
      /** page size */
      _limit: number;
    }

    /** common search params of table */
    type SearchParams<T = any> = CommonType.RecordNullable<
      T &
        PaginatingParams & {
          _sort?: string;
          _order?: string;
          _embed?: string;
          _expand?: string;
          q?: string;
        }
    >;

    /** common create params of table */
    type CreateParams<T = any> = CommonType.RecordNullable<
      Omit<T, 'id' | 'createdBy' | 'createdAt' | 'updatedBy' | 'updatedAt'>
    >;

    /** common update params of table */
    type UpdateParams<T = any> = CommonType.RecordNullable<
      Omit<T, 'createdBy' | 'createdAt' | 'updatedBy' | 'updatedAt'>
    >;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginParams {
      tenant: string;
      username: string;
      password: string;
    }

    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      id: string;
      username: string;
      nickname: string;
      gender: Common.Gender;
      avatar: string;
      email: string;
      phone: string;
      roles?: System.Role[];
      buttons?: string[];
      tenant?: System.Tenant;
    }

    interface UpdateUserInfoParams {
      nickname: string;
      avatar: string;
      gender: Common.Gender;
      email: string;
      phone: string;
    }

    interface UpdatePasswordParams {
      oldpass: string;
      password: string;
      repass: string;
    }

    interface CustomBackendErrorParams {
      code: string;
      message: string;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }
  }

  /**
   * namespace System
   *
   * backend api module: "system"
   */
  namespace System {
    /** config */
    type Config = Common.Record<{
      name: string;
      code: string;
      summary: string;
      status: boolean;
      params: ConfigParam[];
    }>;

    /** config param */
    type ConfigParam = {
      id: string;
      name: string;
      code: string;
      value: string;
      summary: string;
      status: boolean;
    };

    /** config list */
    type ConfigList = Common.PaginatingRecord<Config>;

    /** config search params */
    type ConfigSearchParams = Common.SearchParams<Pick<Config, 'name' | 'code' | 'status'>>;

    /** config create params */
    type ConfigCreateParams = Common.CreateParams<Config>;

    /** config update params */
    type ConfigUpdateParams = Common.UpdateParams<Config>;

    /** dict */
    type Dict = Common.Record<{
      name: string;
      code: string;
      summary: string;
      status: boolean;
      options?: DictOption[];
    }>;

    /** dict option */
    type DictOption = {
      id: string;
      label: string;
      value: string;
      type: 'default' | 'success' | 'error' | 'warning' | 'primary' | 'info';
      status: boolean;
    };

    /** dict list */
    type DictList = Common.PaginatingRecord<Dict>;

    /** dict search params */
    type DictSearchParams = Common.SearchParams<Pick<Dict, 'name' | 'code' | 'status'>>;

    /** dict create params */
    type DictCreateParams = Common.CreateParams<Dict>;

    /** dict update params */
    type DictUpdateParams = Common.UpdateParams<Dict>;

    /**
     * menu type
     *
     * - 1: dir
     * - 2: menu
     */
    type MenuType = 1 | 2;

    /** menu layout */
    type MenuLayout = 'default' | 'base' | 'blank';

    /** menu */
    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      'order' | 'href' | 'constant' | 'hideInMenu' | 'keepAlive' | 'multiTab'
    >;

    /** menu */
    type Menu = Common.Record<{
      menuType: MenuType;
      menuName: string;
      routeName: string;
      routePath: string;
      layout: MenuLayout;
      component: string;
      icon: string;
      status: boolean;
      parentId: number;
      children?: Menu[];
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingRecord<Menu>;

    /** menu search params */
    type MenuSearchParams = Common.SearchParams<Pick<Menu, 'menuName' | 'routeName' | 'constant' | 'status'>>;

    /** menu create params */
    type MenuCreateParams = Common.CreateParams<Menu>;

    /** menu update params */
    type MenuUpdateParams = Common.UpdateParams<Menu>;

    /** api */
    type Api = Common.Record<{
      path: string;
      method: Common.HttpMethod;
      tags: string[];
      summary: string;
      status: boolean;
    }>;

    /** api list */
    type ApiList = Common.PaginatingRecord<Api>;

    /** api search params */
    type ApiSearchParams = Common.SearchParams<Pick<Api, 'path' | 'method' | 'status'>>;

    /** api create params */
    type ApiCreateParams = Common.CreateParams<Api>;

    /** api update params */
    type ApiUpdateParams = Common.UpdateParams<Api>;

    /** dept */
    type Dept = Common.Record<{
      name: string;
      code: string;
      summary: string;
      order: number;
      status: boolean;
      parentId: number;
    }>;

    /** dept list */
    type DeptList = Common.PaginatingRecord<Dept>;

    /** dept search params */
    type DeptSearchParams = Common.SearchParams<Pick<Dept, 'name' | 'code' | 'status'>>;

    /** dept create params */
    type DeptCreateParams = Common.CreateParams<Dept>;

    /** dept update params */
    type DeptUpdateParams = Common.UpdateParams<Dept>;

    /** tenant */
    type Tenant = Common.Record<{
      name: string;
      code: string;
      summary: string;
      status: boolean;
      expiredAt: string;
    }>;

    /** tenant list */
    type TenantList = Common.PaginatingRecord<Tenant>;

    /** tenant search params */
    type TenantSearchParams = Common.SearchParams<Pick<Tenant, 'name' | 'code' | 'status'>>;

    /** tenant create params */
    type TenantCreateParams = Common.CreateParams<Tenant>;

    /** tenant update params */
    type TenantUpdateParams = Common.UpdateParams<Tenant>;

    /** role */
    type Role = Common.Record<{
      name: string;
      code: string;
      summary: string;
      status: boolean;
      home?: string | null;
      menuIds?: number[];
      apiIds?: number[];
    }>;

    /** role list */
    type RoleList = Common.PaginatingRecord<Role>;

    /** role search params */
    type RoleSearchParams = Common.SearchParams<Pick<Role, 'name' | 'code' | 'status'>>;

    /** role create params */
    type RoleCreateParams = Common.CreateParams<Role>;

    /** role update params */
    type RoleUpdateParams = Common.UpdateParams<Role>;

    /** role permit params */
    type RolePermitParams = CommonType.RecordNullable<Pick<Role, 'id' | 'home' | 'menuIds' | 'apiIds'>>;

    /** user */
    type User = Common.Record<{
      username: string;
      password?: string;
      nickname: string;
      gender: Common.Gender;
      phone: string;
      email: string;
      avatar: string;
      status: boolean;
      roleIds: number[];
      tenantId: number | null;
      roles?: Role[];
      tenant?: Tenant;
    }>;

    /** user list */
    type UserList = Common.PaginatingRecord<User>;

    /** user search params */
    type UserSearchParams = Common.SearchParams<
      Pick<User, 'username' | 'nickname' | 'gender' | 'phone' | 'email' | 'status' | 'tenantId'>
    >;

    /** user create params */
    type UserCreateParams = Common.CreateParams<User>;

    /** user update params */
    type UserUpdateParams = Common.UpdateParams<User>;

    /** user password params */
    type UserPasswordParams = CommonType.RecordNullable<Pick<User, 'id' | 'password'>>;

    /** log */
    type Log = Common.Record<{
      userId: number;
      path: string;
      method: Common.HttpMethod;
      code: number;
      params: object;
      time: number;
      userAgent: string;
      clientIp: string;
      user?: User;
    }>;

    /** log list */
    type LogList = Common.PaginatingRecord<Log>;

    /** log search params */
    type LogSearchParams = Common.SearchParams<Pick<Log, 'userId' | 'path' | 'method' | 'code' | 'clientIp'>>;
  }

  /**
   * namespace Cms
   *
   * backend api module: "cms"
   */
  namespace Cms {
    /** meta */
    type Meta = Common.Record<{
      name: string;
      slug: string;
      cover: string;
      order: number;
      summary: string;
      status: boolean;
      parentId: number;
    }>;

    /** meta list */
    type MetaList = Common.PaginatingRecord<Meta>;

    /** meta search params */
    type MetaSearchParams = Common.SearchParams<Pick<Meta, 'name' | 'slug' | 'status'>>;

    /** meta create params */
    type MetaCreateParams = Common.CreateParams<Meta>;

    /** meta update params */
    type MetaUpdateParams = Common.UpdateParams<Meta>;

    /** post */
    type Post = Common.Record<{
      metaId: number;
      title: string;
      summary: string;
      content: string;
      slug: string;
      cover: string;
      files: Common.UploadFile[];
      author: string;
      from: string;
      password: string;
      tags: string[];
      order: number;
      flag: number[];
      status: boolean;
      meta?: Meta;
    }>;

    /** post list */
    type PostList = Common.PaginatingRecord<Post>;

    /** post search params */
    type PostSearchParams = Common.SearchParams<Pick<Post, 'title' | 'slug' | 'flag' | 'metaId' | 'status'>>;

    /** post create params */
    type PostCreateParams = Common.CreateParams<Post>;

    /** post update params */
    type PostUpdateParams = Common.UpdateParams<Post>;
  }

  /**
   * namespace Wms
   *
   * backend api module: "wms"
   */
  namespace Wms {
    /** area */
    type Area = Common.Record<{
      name: string;
      code: string;
      type: number | null;
      summary: string;
      order: number;
      status: boolean;
      parentId: number;
      parentPath: number[];
    }>;

    /** area list */
    type AreaList = Common.PaginatingRecord<Area>;

    /** area search params */
    type AreaSearchParams = Common.SearchParams<Pick<Area, 'name' | 'code' | 'type' | 'status'>>;

    /** area create params */
    type AreaCreateParams = Common.CreateParams<Area>;

    /** area update params */
    type AreaUpdateParams = Common.UpdateParams<Area>;

    /** staff */
    type Staff = Common.Record<{
      username: string;
      password?: string;
      nickname: string;
      gender: Common.Gender;
      phone: string;
      email: string;
      avatar: string;
      status: boolean;
      roleIds: number[];
    }>;

    /** staff list */
    type StaffList = Common.PaginatingRecord<Staff>;

    /** staff search params */
    type StaffSearchParams = Common.SearchParams<
      Pick<Staff, 'username' | 'nickname' | 'gender' | 'phone' | 'email' | 'status'>
    >;

    /** staff create params */
    type StaffCreateParams = Common.CreateParams<Staff>;

    /** staff update params */
    type StaffUpdateParams = Common.UpdateParams<Staff>;

    /** staff password params */
    type StaffPasswordParams = CommonType.RecordNullable<Pick<Staff, 'id' | 'password'>>;

    /** partner */
    type Partner = Common.Record<{
      name: string;
      type: number[];
      level: number;
      summary: string;
      contact: string;
      phone: string;
      email: string;
      area: string | null;
      address: string;
      bankName: string;
      bankAccount: string;
      order: number;
      status: boolean;
    }>;

    /** partner list */
    type PartnerList = Common.PaginatingRecord<Partner>;

    /** partner search params */
    type PartnerSearchParams = Common.SearchParams<
      Pick<Partner, 'name' | 'status'> & {
        type?: number;
      }
    >;

    /** partner create params */
    type PartnerCreateParams = Common.CreateParams<Partner>;

    /** partner update params */
    type PartnerUpdateParams = Common.UpdateParams<Partner>;

    /** item */
    type Item = Common.Record<{
      name: string;
      code: string;
      summary: string;
      order: number;
      status: boolean;
      metaId: number;
    }>;

    /** item list */
    type ItemList = Common.PaginatingRecord<Item>;

    /** item search params */
    type ItemSearchParams = Common.SearchParams<Pick<Item, 'name' | 'code' | 'status' | 'metaId'>>;

    /** item create params */
    type ItemCreateParams = Common.CreateParams<Item>;

    /** item update params */
    type ItemUpdateParams = Common.UpdateParams<Item>;

    /** meta */
    type Meta = Common.Record<{
      name: string;
      order: number;
      status: boolean;
      parentId: number;
      parentPath: number[];
    }>;

    /** meta list */
    type MetaList = Common.PaginatingRecord<Meta>;

    /** meta search params */
    type MetaSearchParams = Common.SearchParams<Pick<Meta, 'name' | 'status' | 'parentId'>>;

    /** meta create params */
    type MetaCreateParams = Common.CreateParams<Meta>;

    /** meta update params */
    type MetaUpdateParams = Common.UpdateParams<Meta>;

    /** unit */
    type Unit = Common.Record<{
      name: string;
      order: number;
      status: boolean;
    }>;

    /** unit list */
    type UnitList = Common.PaginatingRecord<Unit>;

    /** unit search params */
    type UnitSearchParams = Common.SearchParams<Pick<Unit, 'name' | 'status'>>;

    /** unit create params */
    type UnitCreateParams = Common.CreateParams<Unit>;

    /** unit update params */
    type UnitUpdateParams = Common.UpdateParams<Unit>;

    /** sku */
    type Sku = Common.Record<{
      name: string;
      code: string;
      unit: string | null;
      precision: number;
      min: number;
      summary: string;
      order: number;
      status: boolean;
      itemId: number;
      attrs: Common.Option[];
      item?: Item;
    }>;

    /** sku list */
    type SkuList = Common.PaginatingRecord<Sku>;

    /** sku search params */
    type SkuSearchParams = Common.SearchParams<Pick<Sku, 'name' | 'code' | 'status' | 'itemId'>>;

    /** sku create params */
    type SkuCreateParams = Common.CreateParams<Sku>;

    /** sku update params */
    type SkuUpdateParams = Common.UpdateParams<Sku>;

    /** print custom size */
    type CustomSize = {
      width: number;
      height: number;
      unit: string;
    };

    /** print size */
    type PrintSize = Common.Record<{
      name: string;
      paperSize: string;
      orientation: string;
      customSize: CustomSize;
      summary: string;
      order: number;
      status: boolean;
    }>;

    /** print size list */
    type PrintSizeList = Common.PaginatingRecord<PrintSize>;

    /** print size search params */
    type PrintSizeSearchParams = Common.SearchParams<Pick<PrintSize, 'name' | 'status'>>;

    /** print size create params */
    type PrintSizeCreateParams = Common.CreateParams<PrintSize>;

    /** print size update params */
    type PrintSizeUpdateParams = Common.UpdateParams<PrintSize>;

    /** stock */
    type Stock = Common.Record<{
      metaId: number;
      itemId: number | null;
      skuId: number | null;
      areaId: number | null;
      areaPath: number[];
      num: number;
      status: boolean;
      item?: Item;
      sku?: Sku;
    }>;

    /** stock list */
    type StockList = Common.PaginatingRecord<Stock>;

    /** stock search params */
    type StockSearchParams = Common.SearchParams<Pick<Stock, 'itemId' | 'skuId' | 'areaId'>>;

    /** stock create params */
    type StockCreateParams = Common.CreateParams<Stock>;

    /** stock update params */
    type StockUpdateParams = Common.UpdateParams<Stock>;

    /** log */
    type Log = Common.Record<{
      itemId: number;
      skuId: number;
      type: number;
      relatedNo: string;
      num: number;
      remain: number;
      summary: string;
      item?: Item;
      sku?: Sku;
    }>;

    /** log list */
    type LogList = Common.PaginatingRecord<Log>;

    /** log search params */
    type LogSearchParams = Common.SearchParams<Pick<Log, 'itemId' | 'skuId' | 'type' | 'relatedNo'>>;

    /** log create params */
    type LogCreateParams = Common.CreateParams<Log>;

    /** log update params */
    type LogUpdateParams = Common.UpdateParams<Log>;

    /** area num */
    type AreaNum = {
      areaId: number | null;
      areaPath: number[];
      num: number;
    };

    /** receive */
    type Receive = Common.Record<{
      code: string;
      type: number;
      summary: string;
      status: number;
      relatedNo: string;
      partnerId: number | null;
      partner?: Partner;
    }>;

    /** receive list */
    type ReceiveList = Common.PaginatingRecord<Receive>;

    /** receive search params */
    type ReceiveSearchParams = Common.SearchParams<
      Pick<Receive, 'code' | 'type' | 'status' | 'relatedNo' | 'partnerId'>
    >;

    /** receive create params */
    type ReceiveCreateParams = Common.CreateParams<Receive>;

    /** receive update params */
    type ReceiveUpdateParams = Common.UpdateParams<Receive>;

    /** receive sku */
    type ReceiveSku = Common.Record<{
      receiveId: number;
      metaId: number | null;
      itemId: number | null;
      skuId: number | null;
      num: number;
      areaNum: AreaNum[];
      batchNo: string;
      status: boolean;
      receive?: Receive;
      item?: Item;
      sku?: Sku;
    }>;

    /** receive sku list */
    type ReceiveSkuList = Common.PaginatingRecord<ReceiveSku>;

    /** receive sku search params */
    type ReceiveSkuSearchParams = Common.SearchParams<
      Pick<ReceiveSku, 'receiveId' | 'itemId' | 'skuId' | 'status' | 'batchNo'>
    >;

    /** receive sku create params */
    type ReceiveSkuCreateParams = Common.CreateParams<ReceiveSku>;

    /** receive sku update params */
    type ReceiveSkuUpdateParams = Common.UpdateParams<ReceiveSku>;

    /** deliver */
    type Deliver = Common.Record<{
      code: string;
      type: number;
      summary: string;
      status: number;
      relatedNo: string;
      partnerId: number | null;
      partner?: Partner;
    }>;

    /** deliver list */
    type DeliverList = Common.PaginatingRecord<Deliver>;

    /** deliver search params */
    type DeliverSearchParams = Common.SearchParams<
      Pick<Deliver, 'code' | 'type' | 'status' | 'relatedNo' | 'partnerId'>
    >;

    /** deliver create params */
    type DeliverCreateParams = Common.CreateParams<Deliver>;

    /** deliver update params */
    type DeliverUpdateParams = Common.UpdateParams<Deliver>;

    /** deliver sku */
    type DeliverSku = Common.Record<{
      deliverId: number;
      metaId: number | null;
      itemId: number | null;
      skuId: number | null;
      num: number;
      areaNum: AreaNum[];
      pcs: number;
      status: boolean;
      deliver?: Deliver;
      item?: Item;
      sku?: Sku;
    }>;

    /** deliver sku list */
    type DeliverSkuList = Common.PaginatingRecord<DeliverSku>;

    /** deliver sku search params */
    type DeliverSkuSearchParams = Common.SearchParams<Pick<DeliverSku, 'deliverId' | 'itemId' | 'skuId' | 'status'>>;

    /** deliver sku create params */
    type DeliverSkuCreateParams = Common.CreateParams<DeliverSku>;

    /** deliver sku update params */
    type DeliverSkuUpdateParams = Common.UpdateParams<DeliverSku>;

    /** transfer */
    type Transfer = Common.Record<{
      code: string;
      type: number;
      summary: string;
      status: number;
    }>;

    /** transfer list */
    type TransferList = Common.PaginatingRecord<Transfer>;

    /** transfer search params */
    type TransferSearchParams = Common.SearchParams<Pick<Transfer, 'code' | 'type' | 'status'>>;

    /** transfer create params */
    type TransferCreateParams = Common.CreateParams<Transfer>;

    /** transfer update params */
    type TransferUpdateParams = Common.UpdateParams<Transfer>;

    /** transfer sku */
    type TransferSku = Common.Record<{
      transferId: number;
      metaId: number | null;
      itemId: number | null;
      skuId: number | null;
      num: number;
      fromAreaNum: AreaNum[];
      toAreaNum: AreaNum[];
      status: boolean;
      transfer?: Transfer;
      item?: Item;
      sku?: Sku;
    }>;

    /** transfer sku list */
    type TransferSkuList = Common.PaginatingRecord<TransferSku>;

    /** transfer sku search params */
    type TransferSkuSearchParams = Common.SearchParams<Pick<TransferSku, 'transferId' | 'itemId' | 'skuId' | 'status'>>;

    /** transfer sku create params */
    type TransferSkuCreateParams = Common.CreateParams<TransferSku>;

    /** transfer sku update params */
    type TransferSkuUpdateParams = Common.UpdateParams<TransferSku>;

    /** check */
    type Check = Common.Record<{
      code: string;
      type: number;
      summary: string;
      status: number;
      areaId: number | null;
      areaPath: number[];
    }>;

    /** check list */
    type CheckList = Common.PaginatingRecord<Check>;

    /** check search params */
    type CheckSearchParams = Common.SearchParams<Pick<Check, 'code' | 'type' | 'status' | 'areaId'>>;

    /** check create params */
    type CheckCreateParams = Common.CreateParams<Check>;

    /** check update params */
    type CheckUpdateParams = Common.UpdateParams<Check>;

    /** check sku */
    type CheckSku = Common.Record<{
      checkId: number;
      metaId: number | null;
      itemId: number | null;
      skuId: number | null;
      num: number;
      remain: number;
      status: boolean;
      check?: Check;
      item?: Item;
      sku?: Sku;
    }>;

    /** check sku list */
    type CheckSkuList = Common.PaginatingRecord<CheckSku>;

    /** check sku search params */
    type CheckSkuSearchParams = Common.SearchParams<Pick<CheckSku, 'checkId' | 'itemId' | 'skuId' | 'status'>>;

    /** check sku create params */
    type CheckSkuCreateParams = Common.CreateParams<CheckSku>;

    /** check sku update params */
    type CheckSkuUpdateParams = Common.UpdateParams<CheckSku>;

    /** pack */
    type Pack = Common.Record<{
      code: string;
      deliverId: number | null;
      itemId: number | null;
      skuId: number | null;
      num: number;
      status: boolean;
      deliver?: Deliver;
      item?: Item;
      sku?: Sku;
    }>;

    /** pack list */
    type PackList = Common.PaginatingRecord<Pack>;

    /** pack search params */
    type PackSearchParams = Common.SearchParams<Pick<Pack, 'code' | 'deliverId' | 'itemId' | 'skuId' | 'status'>>;

    /** pack create params */
    type PackCreateParams = Common.CreateParams<Pack>;

    /** pack update params */
    type PackUpdateParams = Common.UpdateParams<Pack>;
  }
}

import type { TreeOption } from 'naive-ui';
import dayjs from 'dayjs';
import { customAlphabet } from 'nanoid';
import { type PrintAreaOption, VuePrintNext } from 'vue-print-next';
import { $t } from '@/locales';

/**
 * Transform record to option
 *
 * @example
 *   ```ts
 *   const record = {
 *     key1: 'label1',
 *     key2: 'label2'
 *   };
 *   const options = transformRecordToOption(record);
 *   // [
 *   //   { value: 'key1', label: 'label1' },
 *   //   { value: 'key2', label: 'label2' }
 *   // ]
 *   ```;
 *
 * @param record
 */
export function transformRecordToOption<T extends Record<string, string>>(record: T) {
  return Object.entries(record).map(([value, label]) => ({
    value,
    label
  })) as CommonType.Option<keyof T>[];
}

/**
 * Translate options
 *
 * @param options
 */
export function translateOptions(options: CommonType.Option<string>[]) {
  return options.map(option => ({
    ...option,
    label: $t(option.label as App.I18n.I18nKey)
  }));
}

/**
 * Toggle html class
 *
 * @param className
 */
export function toggleHtmlClass(className: string) {
  function add() {
    document.documentElement.classList.add(className);
  }

  function remove() {
    document.documentElement.classList.remove(className);
  }

  return {
    add,
    remove
  };
}

/**
 * Convert To Tree
 *
 * @param arr
 * @param key
 */
export function convertToTree(arr: any, key: string = 'parentId') {
  const map: any[] = [];
  const tree: any[] = [];

  arr.forEach((item: any) => {
    map[item.id] = { ...item, children: [] };
  });

  arr.forEach((item: any) => {
    if (item[key]) {
      map[item[key]]?.children.push(map[item.id]);
    } else {
      tree.push(map[item.id]);
    }
  });

  map.forEach((item: any) => {
    if (item.children.length === 0) delete item.children;
  });

  return tree;
}

/**
 * Disable Tree Item
 *
 * @param tree
 * @param id
 */
export function disableTreeItem(tree: any, ids: number[]) {
  return tree.map((item: any) => {
    if (ids.includes(item.id)) {
      return { ...item, disabled: true, children: [] };
    } else if (item.children) {
      return { ...item, children: disableTreeItem(item.children, ids) };
    }
    return item;
  });
}

/**
 * Convert Api To Tree
 *
 * @param apis
 */
export function convertApiToTree(apis: any) {
  const root: any = [];
  apis.forEach((item: Api.System.Api) => {
    let currentLevel = root;
    item.tags.forEach((tag, index) => {
      let node = currentLevel.find((n: any) => n.id === `tag$${tag}`);
      if (!node) {
        node = { id: `tag$${tag}`, summary: tag, children: [] };
        currentLevel.push(node);
      }
      if (index === item.tags.length - 1) {
        node.children.push(item);
      }
      currentLevel = node.children;
    });
  });

  return root;
}

/** Suggest Code */
export function suggestCode(prefix: string = 'WL') {
  const nanoid = customAlphabet('1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ', 6);
  return prefix + dayjs().format('YYYYMMDD') + nanoid();
}

/** Tree Select Tag */
export function treeSelectTag(option: any, options: TreeOption[] | undefined, label: string = 'label') {
  const findPath = (nodes: any[], targetId: any, currentPath: string[] = []): string[] | null => {
    for (const node of nodes) {
      const newPath = [...currentPath, node[label]];
      if (node.id === targetId) {
        return newPath;
      }
      if (node.children && node.children.length > 0) {
        const result = findPath(node.children, targetId, newPath);
        if (result) return result;
      }
    }
    return null;
  };

  const path = findPath(options || [], option.id);
  return path ? path.join(' / ') : option[label];
}

export function showArea(path: number[], areas: Api.Wms.Area[], code: boolean = true) {
  return path.map(id => areas.find(item => item.id === id)?.[code ? 'code' : 'name']).join('-');
}

// 快捷打印
export function fastPrint(options: PrintAreaOption = {}) {
  // eslint-disable-next-line no-new
  new VuePrintNext({
    preview: true,
    previewTools: {
      zoom: true,
      theme: true,
      fullscreen: true
    },
    paperSize: 'A4',
    orientation: 'portrait',
    ...options
  });
}

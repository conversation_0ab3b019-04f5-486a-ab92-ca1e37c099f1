<script setup lang="tsx">
import { ref } from 'vue';
import { NButton, NCard, NDropdown, NInput, NScrollbar, NTree } from 'naive-ui';
import type { TreeOption } from 'naive-ui';
import { metaApi } from '@/service/api/wms';
import { $t } from '@/locales';
import MetaModal from './MetaModal.vue';

interface Emits {
  (e: 'search'): void;
  (e: 'update'): void;
}
const emit = defineEmits<Emits>();

const metaId = defineModel<number | null | undefined>('value', { required: true });

defineProps<{
  metaOptions?: TreeOption[];
}>();

const pattern = ref('');

const nodeProps = ({ option }: { option: TreeOption }) => {
  return {
    onClick() {
      metaId.value = metaId.value === (option.id as number) ? undefined : (option.id as number);
      emit('search');
    }
  };
};

const operateType = ref<NaiveUI.TableOperateType | 'addChild'>('add');
const modalVisible = ref(false);
const editingData = ref<Api.Wms.Meta | null>(null);

const operateOptions = [
  {
    label: $t('common.add'),
    key: 'addChild',
    props: {
      style: {
        color: 'rgb(var(--primary-color))'
      }
    }
  },
  {
    label: $t('common.edit'),
    key: 'edit'
  },
  {
    label: $t('common.delete'),
    key: 'delete',
    props: {
      style: {
        color: 'rgb(var(--error-color))'
      }
    }
  }
];

function renderSuffix({ option }: { option: TreeOption }) {
  return (
    <NDropdown
      options={operateOptions}
      size="small"
      onSelect={(key: string) => handleOperate(key, option as Api.Wms.Meta)}
    >
      <NButton size="tiny" quaternary>
        <icon-ph-dots-three class="text-icon" />
      </NButton>
    </NDropdown>
  );
}

function handleAdd() {
  operateType.value = 'add';
  modalVisible.value = true;
}

function handleEdit(data: Api.Wms.Meta) {
  operateType.value = 'edit';
  modalVisible.value = true;
  editingData.value = data;
}

async function handleDelete(id: number) {
  window.$dialog?.create({
    title: $t('common.warning'),
    content: $t('common.confirmDelete'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: async () => {
      const { error } = await metaApi.del(id);
      if (!error) {
        emit('update');
        window.$message?.success($t('common.deleteSuccess'));
      } else {
        window.$message?.error(error.message);
      }
    }
  });
}

function handleAddChild(data: Api.Wms.Meta | null) {
  operateType.value = 'addChild';
  modalVisible.value = true;
  editingData.value = data;
}

function handleOperate(key: string, data: Api.Wms.Meta) {
  if (key === 'addChild') {
    handleAddChild(data);
  } else if (key === 'edit') {
    handleEdit(data);
  } else if (key === 'delete') {
    handleDelete(data.id);
  }
}
</script>

<template>
  <NCard title="所属分类" :bordered="false" size="small" class="h-full">
    <template #header-extra>
      <NButton :focusable="false" quaternary @click="handleAdd">
        <icon-ph-plus class="text-icon" />
      </NButton>
    </template>
    <NInput v-model:value="pattern" placeholder="搜索" />
    <NScrollbar class="m-t-12px">
      <NTree
        :show-irrelevant-nodes="false"
        :pattern="pattern"
        :data="metaOptions"
        key-field="id"
        label-field="name"
        accordion
        block-line
        :node-props="nodeProps"
        :render-suffix="renderSuffix"
      />
    </NScrollbar>
    <MetaModal
      v-model:visible="modalVisible"
      :operate-type="operateType"
      :meta-options="metaOptions"
      :row-data="editingData"
      @submitted="emit('update')"
    />
  </NCard>
</template>

<style lang="scss" scoped>
:deep(.n-card__content) {
  display: flex;
  flex-direction: column;
  height: calc(100% - 50px);
}
:deep(.n-tree) {
  .n-tree-node {
    padding: 8px 16px;
    &.n-tree-node--selected {
      color: rgb(var(--primary-color));
      background-color: rgb(var(--primary-500-color) / 0.1);
    }
  }
}
</style>

<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import emitter from '@/utils/mitt';
import Main from './modules/main/index.vue';
import Sku from './modules/sku/index.vue';

const receive = ref<Api.Wms.Receive | null>(null);

emitter.on('showReceiveSku', (data: Api.Wms.Receive | null) => {
  receive.value = data;
});

onUnmounted(() => {
  emitter.off('showReceiveSku');
});
</script>

<template>
  <div class="relative m-12px h-full">
    <Transition name="fade" mode="out-in">
      <Main v-show="!receive" key="main" class="absolute inset-0" />
    </Transition>
    <Transition name="fade" mode="out-in">
      <Sku v-if="receive" key="sku" class="absolute inset-0" :receive="receive" />
    </Transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

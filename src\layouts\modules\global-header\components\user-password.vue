<script setup lang="ts">
import { ref, watch } from 'vue';
import { authApi } from '@/service/api/base';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.Auth.UpdatePasswordParams, 'oldpass' | 'password' | 'repass'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    oldpass: '',
    password: '',
    repass: ''
  };
}

type RuleKey = Extract<keyof Model, 'oldpass' | 'password' | 'repass'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  oldpass: defaultRequiredRule,
  password: defaultRequiredRule,
  repass: defaultRequiredRule
};

function handleInitModel() {
  model.value = createDefaultModel();
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();
    // request
    const { error } = await authApi.updatePassword(model.value);
    if (!error) {
      window.$message?.success('修改密码成功');
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error('修改密码失败');
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NModal v-model:show="visible" title="修改密码" preset="card" class="w-500px">
    <NForm ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
      <NFormItem label="原密码" path="oldpass">
        <NInput v-model:value="model.oldpass" type="password" placeholder="请输入原密码" clearable />
      </NFormItem>
      <NFormItem label="新密码" path="password">
        <NInput v-model:value="model.password" type="password" placeholder="请输入新密码" clearable />
      </NFormItem>
      <NFormItem label="确认密码" path="repass">
        <NInput v-model:value="model.repass" type="password" placeholder="请输入确认密码" clearable />
      </NFormItem>
    </NForm>
    <template #footer>
      <NSpace justify="end" :size="16">
        <NButton @click="visible = false">取消</NButton>
        <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { areaApi } from '@/service/api/wms';
import { showArea } from '@/utils/common';
import QRCode from '@/components/custom/qr-code.vue';

const props = defineProps<{
  id: number;
}>();

const areas = ref<Api.Wms.Area[] | null>(null);

const data = computed(() => areas.value?.find(item => item.id === props.id) || null);

async function getData() {
  const { data: areaList, error } = await areaApi.list({
    _page: 1,
    _limit: 1000,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: true
  });
  if (!error) {
    areas.value = areaList.records;
  }
}

onMounted(async () => {
  await getData();
});
</script>

<template>
  <div v-if="data" class="print">
    <div class="flex items-center gap-x-16px">
      <div class="h-180px">
        <QRCode class="h-full" :value="showArea([...data.parentPath, data.id], areas || [], false)" />
      </div>
      <div class="text-24px">
        <div class="flex flex-col">
          <div class="font-bold">仓库名称：</div>
          <div>{{ showArea([...data.parentPath, data.id], areas || [], false) }}</div>
        </div>
        <div class="m-t-24px flex flex-col">
          <div class="font-bold">仓库编码：</div>
          <div>{{ showArea([...data.parentPath, data.id], areas || [], true) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>

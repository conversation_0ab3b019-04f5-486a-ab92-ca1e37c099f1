<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { checkApi } from '@/service/api/wms';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';
import { suggestCode, treeSelectTag } from '@/utils/common';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Wms.Check | null;
  /** the area options */
  areaOptions: TreeOption[];
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.Wms.Check, 'code' | 'type' | 'summary' | 'status' | 'areaId' | 'areaPath'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    code: suggestCode('PD'),
    type: 1,
    summary: '',
    status: 2,
    areaId: null,
    areaPath: []
  };
}

type RuleKey = Extract<keyof Model, 'code' | 'type'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  code: defaultRequiredRule,
  type: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增盘点单',
    edit: '编辑盘点单'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit(fn?: () => void) {
  loading.value = true;
  try {
    // 执行额外函数
    if (fn) {
      fn();
    }

    await validate();
    // request
    const { error } = props.operateType === 'edit' ? await checkApi.save(model.value) : await checkApi.add(model.value);

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

function handleAreaChange(value: number, area: Api.Wms.Area) {
  model.value.areaPath = value ? [...area.parentPath, area.id] : [];
}
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="单号" path="code">
          <NInput v-model:value="model.code" placeholder="请输入单号" disabled clearable />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NSelect
            v-model:value="model.type"
            :options="useDict('number').items('CheckType')"
            placeholder="请选择类型"
            :disabled="model.status === 3"
            clearable
          />
        </NFormItem>
        <NFormItem label="库位" path="areaId">
          <NTreeSelect
            v-model:value="model.areaId"
            :options="areaOptions"
            key-field="id"
            label-field="name"
            :render-tag="({ option }) => treeSelectTag(option, areaOptions, 'name')"
            placeholder="请选择库位"
            clearable
            @update:value="handleAreaChange"
          />
        </NFormItem>
        <NFormItem label="备注" path="summary">
          <NInput v-model:value="model.summary" placeholder="请输入备注" type="textarea" clearable />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton v-if="model.status === 2" :loading="loading" @click="handleSubmit(() => (model.status = 1))">
            暂存草稿
          </NButton>
          <NButton
            v-if="model.status === 1 || operateType === 'add'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 2))"
          >
            发布待审
          </NButton>
          <NButton
            v-if="model.status === 2 && operateType === 'edit'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 3))"
          >
            确认审核
          </NButton>
          <NButton
            v-if="model.status === 3 && operateType === 'edit'"
            type="primary"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 4))"
          >
            确认完成
          </NButton>
          <NButton
            v-if="model.status === 3 && operateType === 'edit'"
            type="error"
            :loading="loading"
            @click="handleSubmit(() => (model.status = 5))"
          >
            作废
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

<script setup lang="ts">
import { onBeforeUnmount, onMounted, ref } from 'vue';
import Header from './modules/Header.vue';
import Area from './modules/Area.vue';
import Stat from './modules/Stat.vue';
import Stock from './modules/Stock.vue';
import Bill from './modules/Bill.vue';
import ItemRate from './modules/ItemRate.vue';
import ReceiveTrend from './modules/ReceiveTrend.vue';
import DeliverTrend from './modules/DeliverTrend.vue';
import Warning from './modules/Warning.vue';
import Count from './modules/Count.vue';

// 自适应
const fontSize = ref(1);
const autofit = () => {
  document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
  fontSize.value =
    document.documentElement.clientWidth / document.documentElement.clientHeight < 16 / 9
      ? document.documentElement.clientWidth / 1920
      : document.documentElement.clientHeight / 1080;
  document.documentElement.style.fontSize = `${fontSize.value}px`;
};

async function getData() {}

onMounted(() => {
  // 自适应
  autofit();
  // 监听窗口变化
  window.addEventListener('resize', autofit);

  // 初始化数据
  getData();
  // 10分钟更新一次数据
  setInterval(() => getData(), 600 * 1000);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', autofit);
  // 移除增加的样式
  document.documentElement.style.removeProperty('--vh');
  document.documentElement.style.removeProperty('font-size');
});

const title = ref('仓库管理系统');
</script>

<template>
  <div class="bi size-full">
    <Header :title="title" />
    <Area />
    <Stat />
    <Stock />
    <Bill />
    <ItemRate />
    <ReceiveTrend />
    <DeliverTrend />
    <Count />
    <Warning />
  </div>
</template>

<style scoped lang="scss">
@font-face {
  font-family: 'AlimamaShuHeiTi';
  src: url('@/assets/fonts/AlimamaShuHeiTi-Bold.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

* {
  box-sizing: border-box;
}

.bi {
  position: relative;
  background: #192a44 url(@/assets/imgs/bi/<EMAIL>) no-repeat center;
  background-size: 100% 100%;
}

:deep(.box) {
  position: absolute;
  z-index: 2;
  background: rgba(4, 45, 66, 0.5);
  padding: 10rem;
  color: #cfedff;
  font-size: 14rem;

  .hd {
    width: 100%;
    height: 42rem;
    background: url(@/assets/imgs/bi/<EMAIL>) no-repeat left center;
    background-size: auto 100%;
    padding-left: 55rem;
    line-height: 42rem;
    font-family: 'AlimamaShuHeiTi';
    font-size: 16px;
    color: #cfedff;
    text-shadow: 0px 0px 15px #009caf;
    font-style: italic;
  }
}
</style>

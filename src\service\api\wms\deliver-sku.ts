import { request } from '@/service/request';

export const deliverSkuApi = {
  // 获取出库规格列表
  list: (params: Api.Wms.DeliverSkuSearchParams) =>
    request<Api.Wms.DeliverSkuList>({
      url: '/wms/deliver-skus',
      params
    }),

  // 创建出库规格
  add: (data: Api.Wms.DeliverSkuCreateParams) =>
    request<null>({
      url: '/wms/deliver-skus',
      method: 'POST',
      data
    }),

  // 更新出库规格
  save: (data: Api.Wms.DeliverSkuUpdateParams) =>
    request<null>({
      url: `/wms/deliver-skus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除出库规格
  del: (id: number) =>
    request<null>({
      url: `/wms/deliver-skus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除出库规格
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/deliver-skus',
      method: 'DELETE',
      data: { ids }
    })
};

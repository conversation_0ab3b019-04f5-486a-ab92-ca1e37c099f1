<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import jrQrcode from 'jr-qrcode';

interface Props {
  value?: string;
  options?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  options: () => ({})
});

const imgBase64 = ref<string>('');

watch(
  () => props.value,
  nval => {
    imgBase64.value = jrQrcode.getQrBase64(nval, props.options);
  }
);

watch(
  () => props.options,
  nval => {
    imgBase64.value = jrQrcode.getQrBase64(props.value, nval);
  }
);

onMounted(() => {
  imgBase64.value = jrQrcode.getQrBase64(props.value, props.options);
});
</script>

<template>
  <img :src="imgBase64" />
</template>

<style></style>

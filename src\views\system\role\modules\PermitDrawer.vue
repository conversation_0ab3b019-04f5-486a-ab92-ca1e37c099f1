<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TreeOption } from 'naive-ui';
import { apiApi, menuApi, roleApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { convertApiToTree, convertToTree } from '@/utils/common';

export type OperateType = 'menu' | 'api';

interface Props {
  /** the type of operation */
  operateType: OperateType;
  /** the edit row data */
  rowData?: Api.System.Role | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.System.Role, 'home' | 'menuIds' | 'apiIds'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    home: null,
    menuIds: [],
    apiIds: []
  };
}

type RuleKey = Extract<keyof Model, 'label' | 'value'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  home: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<OperateType, string> = {
    menu: '菜单权限',
    api: '接口权限'
  };
  return titles[props.operateType];
});

const menuOptions = ref<TreeOption[]>([]);
const apiOptions = ref<TreeOption[]>([]);

async function handleInitModel() {
  model.value = createDefaultModel();

  if (props.rowData) {
    const { id, home, menuIds, apiIds } = props.rowData;
    Object.assign(model.value, { id, home, menuIds, apiIds });
  }

  if (props.operateType === 'menu') {
    const { data: menuList, error: menuError } = await menuApi.list({
      _page: 1,
      _limit: 1000,
      _sort: 'order',
      _order: 'asc',
      constant: false,
      status: true
    });
    if (!menuError) {
      menuOptions.value = convertToTree(menuList.records);
    }
  } else {
    const { data: apiList, error: apiError } = await apiApi.list({
      _page: 1,
      _limit: 1000,
      _sort: 'id',
      _order: 'asc',
      status: true
    });
    if (!apiError) {
      apiOptions.value = convertApiToTree(apiList.records);
    }
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();

    if (props.operateType === 'api') {
      model.value.apiIds = model.value.apiIds?.filter((item: any) => (typeof item === 'number' ? item : item.id));
    }

    const { error } = await roleApi.permit(model.value);
    if (!error) {
      window.$message?.success(`${title.value}成功`);
      visible.value = false;
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const homeOptions = computed(() => {
  const options: CommonType.Option<string>[] = [];
  menuOptions.value?.forEach((item: TreeOption) => {
    options.push({
      value: item.routeName,
      label: item.menuName
    } as CommonType.Option<string>);
  });
  return options;
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm
        v-if="visible && operateType === 'menu'"
        ref="formRef"
        :model="model"
        :rules="rules"
        :label-width="100"
        label-placement="left"
      >
        <NFormItem label="默认首页" path="home">
          <NSelect
            v-model:value="model.home"
            class="w-200px"
            :options="homeOptions"
            placeholder="请选择首页"
            clearable
          />
        </NFormItem>
        <NFormItem label="角色菜单" path="menuIds">
          <NTree
            v-model:checked-keys="model.menuIds"
            :data="menuOptions"
            key-field="id"
            label-field="menuName"
            default-expand-all
            cascade
            checkable
            expand-on-click
            virtual-scroll
            block-line
          />
        </NFormItem>
      </NForm>
      <NForm
        v-if="visible && operateType === 'api'"
        ref="formRef"
        :model="model"
        :rules="rules"
        :label-width="100"
        label-placement="left"
      >
        <NTree
          v-model:checked-keys="model.apiIds"
          :data="apiOptions"
          key-field="id"
          label-field="summary"
          default-expand-all
          cascade
          checkable
          expand-on-click
          virtual-scroll
          block-line
        />
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>

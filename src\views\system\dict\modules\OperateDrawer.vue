<script setup lang="ts">
import { computed, h, ref, watch } from 'vue';
import type { SelectRenderTag } from 'naive-ui';
import { NTag } from 'naive-ui';
import { nanoid } from 'nanoid';
import { dictApi } from '@/service/api/system';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.System.DictOption | null;
  /** the dict */
  dict: Api.System.Dict | null | undefined;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const loading = ref(false);

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type Model = Pick<Api.System.DictOption, 'id' | 'label' | 'value' | 'type' | 'status'>;

const model = ref(createDefaultModel());

function createDefaultModel(): Model {
  return {
    id: nanoid(8),
    label: '',
    value: '',
    type: 'default',
    status: true
  };
}

type RuleKey = Extract<keyof Model, 'label' | 'value'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  label: defaultRequiredRule,
  value: defaultRequiredRule
};

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增选项',
    edit: '编辑选项'
  };
  return titles[props.operateType];
});

function handleInitModel() {
  model.value = createDefaultModel();

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model.value, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  loading.value = true;
  try {
    await validate();

    const options =
      props.operateType === 'add'
        ? (props.dict?.options || []).concat([model.value])
        : props.dict?.options?.map(option => {
            return option.id === model.value.id ? model.value : option;
          });
    // request
    const { error } = await dictApi.save({ ...props.dict, options });

    if (!error) {
      window.$message?.success(`${title.value}成功`);
      closeDrawer();
      emit('submitted');
    }
  } catch {
    window.$message?.error(`${title.value}失败`);
  } finally {
    loading.value = false;
  }
}

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});

const renderTag: SelectRenderTag = ({ option }) => {
  return h(
    NTag,
    {
      type: option.type as 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info',
      onMousedown: (e: FocusEvent) => {
        e.preventDefault();
      }
    },
    { default: () => option.label }
  );
};

const typeOptions = computed(() => {
  return [
    { label: '默认', value: 'default', type: 'default' },
    { label: '主色调', value: 'primary', type: 'primary' },
    { label: '成功', value: 'success', type: 'success' },
    { label: '警告', value: 'warning', type: 'warning' },
    { label: '错误', value: 'error', type: 'error' },
    { label: '信息', value: 'info', type: 'info' }
  ];
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="500">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm v-if="visible" ref="formRef" :model="model" :rules="rules" :label-width="100" label-placement="left">
        <NFormItem label="选项名" path="label">
          <NInput v-model:value="model.label" placeholder="请输入选项名" clearable />
        </NFormItem>
        <NFormItem label="选项值" path="value">
          <NInput v-model:value="model.value" placeholder="请输入选项值" type="textarea" clearable />
        </NFormItem>
        <NFormItem label="类型" path="type">
          <NSelect v-model:value="model.type" :options="typeOptions" :render-tag="renderTag" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSwitch v-model:value="model.status" />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">取消</NButton>
          <NButton type="primary" :loading="loading" @click="handleSubmit">确定</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped lang="scss">
:deep(.n-select) {
  .n-base-selection {
    .n-base-selection-label {
      .n-base-selection-input {
        padding-left: 3px;
      }
    }
  }
}
</style>

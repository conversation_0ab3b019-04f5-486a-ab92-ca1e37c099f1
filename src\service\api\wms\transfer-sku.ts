import { request } from '@/service/request';

export const transferSkuApi = {
  // 获取调拨规格列表
  list: (params: Api.Wms.TransferSkuSearchParams) =>
    request<Api.Wms.TransferSkuList>({
      url: '/wms/transfer-skus',
      params
    }),

  // 创建调拨规格
  add: (data: Api.Wms.TransferSkuCreateParams) =>
    request<null>({
      url: '/wms/transfer-skus',
      method: 'POST',
      data
    }),

  // 更新调拨规格
  save: (data: Api.Wms.TransferSkuUpdateParams) =>
    request<null>({
      url: `/wms/transfer-skus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除调拨规格
  del: (id: number) =>
    request<null>({
      url: `/wms/transfer-skus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除调拨规格
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/transfer-skus',
      method: 'DELETE',
      data: { ids }
    })
};

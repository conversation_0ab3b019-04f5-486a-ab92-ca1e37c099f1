<script setup lang="ts">
import { onUnmounted, ref } from 'vue';
import emitter from '@/utils/mitt';
import Main from './modules/main/index.vue';
import Sku from './modules/sku/index.vue';

const transfer = ref<Api.Wms.Transfer | null>(null);

emitter.on('showTransferSku', (data: Api.Wms.Transfer | null) => {
  transfer.value = data;
});

onUnmounted(() => {
  emitter.off('showTransferSku');
});
</script>

<template>
  <div class="relative m-12px h-full">
    <Transition name="fade" mode="out-in">
      <Main v-show="!transfer" key="main" class="absolute inset-0" />
    </Transition>
    <Transition name="fade" mode="out-in">
      <Sku v-if="transfer" key="sku" class="absolute inset-0" :transfer="transfer" />
    </Transition>
  </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>

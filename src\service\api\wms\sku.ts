import { request } from '@/service/request';

export const skuApi = {
  // 获取规格列表
  list: (params: Api.Wms.SkuSearchParams) =>
    request<Api.Wms.SkuList>({
      url: '/wms/skus',
      params
    }),

  // 获取规格详情
  get: (id: number, params: Api.Wms.SkuSearchParams) =>
    request<Api.Wms.Sku>({
      url: `/wms/skus/${id}`,
      params
    }),

  // 创建规格
  add: (data: Api.Wms.SkuCreateParams) =>
    request<null>({
      url: '/wms/skus',
      method: 'POST',
      data
    }),

  // 更新规格
  save: (data: Api.Wms.SkuUpdateParams) =>
    request<null>({
      url: `/wms/skus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除规格
  del: (id: number) =>
    request<null>({
      url: `/wms/skus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除规格
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/skus',
      method: 'DELETE',
      data: { ids }
    })
};

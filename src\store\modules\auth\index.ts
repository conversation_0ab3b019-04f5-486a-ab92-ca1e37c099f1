import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { authApi } from '@/service/api/base';
import { dictApi } from '@/service/api/system';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import { SetupStoreId } from '@/enum';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { clearAuthStorage, getToken } from './shared';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    id: '',
    username: '',
    nickname: '',
    gender: 0,
    phone: '',
    email: '',
    avatar: '',
    roles: [],
    buttons: [],
    tenant: undefined
  });

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles?.some(role => role.code === VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** User Home */
  const home = computed(() => {
    return userInfo.roles?.[0]?.home || 'home';
  });

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();

    clearAuthStorage();

    // 清理字典
    localStg.remove('dictList');

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /**
   * Login
   *
   * @param data login params
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(data: Api.Auth.LoginParams, redirect = true) {
    startLoading();

    const { data: loginToken, error } = await authApi.login(data);

    if (!error) {
      const pass = await loginByToken(loginToken);

      if (pass) {
        await redirectFromLogin(redirect);

        window.$notification?.success({
          title: $t('page.login.common.loginSuccess'),
          content: $t('page.login.common.welcomeBack', { userName: userInfo.username }),
          duration: 4500
        });
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    localStg.set('refreshToken', loginToken.refreshToken);

    // 2. get user info
    const pass = await getUserInfo();

    if (pass) {
      token.value = loginToken.token;

      return true;
    }

    return false;
  }

  async function getUserInfo() {
    const { data: info, error } = await authApi.getUserInfo();

    if (!error) {
      // update store
      Object.assign(userInfo, info);

      // 3. init dict list
      await initDictList();

      return true;
    }

    return false;
  }

  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();

      if (!pass) {
        resetStore();
      }
    }
  }

  const dictList = ref<Api.System.Dict[]>([]);

  async function initDictList(force = false) {
    dictList.value = localStg.get('dictList') || [];
    if (dictList.value.length === 0 || force) {
      const { data: list, error } = await dictApi.list({
        _page: 1,
        _limit: 1000
      });
      if (!error) {
        dictList.value = list.records;
        localStg.set('dictList', list.records);
      }
    }
  }

  return {
    token,
    userInfo,
    home,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    initUserInfo,
    dictList,
    initDictList
  };
});

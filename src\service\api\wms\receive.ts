import { request } from '@/service/request';

export const receiveApi = {
  // 获取入库单列表
  list: (params: Api.Wms.ReceiveSearchParams) =>
    request<Api.Wms.ReceiveList>({
      url: '/wms/receives',
      params
    }),

  // 获取入库单详情
  get: (id: number, params: Api.Wms.ReceiveSearchParams) =>
    request<Api.Wms.Receive>({
      url: `/wms/receives/${id}`,
      params
    }),

  // 创建入库单
  add: (data: Api.Wms.ReceiveCreateParams) =>
    request<null>({
      url: '/wms/receives',
      method: 'POST',
      data
    }),

  // 更新入库单
  save: (data: Api.Wms.ReceiveUpdateParams) =>
    request<null>({
      url: `/wms/receives/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除入库单
  del: (id: number) =>
    request<null>({
      url: `/wms/receives/${id}`,
      method: 'DELETE'
    }),

  // 批量删除入库单
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/receives',
      method: 'DELETE',
      data: { ids }
    })
};

<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NButton, NPopconfirm } from 'naive-ui';
import type { DataTableSortState } from 'naive-ui';
import { itemApi, skuApi } from '@/service/api/wms';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import emitter from '@/utils/mitt';
import SwitchStatus from '@/components/custom/switch-status.vue';
import PrintSetting from '@/components/common/print-setting.vue';
import OperateDrawer from './modules/OperateDrawer.vue';
import SearchBar from './modules/SearchBar.vue';

const props = defineProps<{
  item: Api.Wms.Item | null;
}>();

const appStore = useAppStore();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  pagination,
  searchParams,
  resetSearchParams,
  updateSearchParams
} = useTable({
  apiFn: skuApi.list,
  showTotal: true,
  apiParams: {
    _page: 1,
    _limit: 10,
    _sort: 'order,id',
    _order: 'desc,asc',
    status: null,
    name: null,
    code: null,
    itemId: props.item?.id || 0
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'name',
      title: '规格名',
      align: 'left',
      ellipsis: true
    },
    {
      key: 'code',
      title: '编码',
      align: 'left'
    },
    {
      key: 'unit',
      title: '单位',
      align: 'left'
    },
    {
      key: 'min',
      title: '预警库存',
      align: 'left'
    },
    {
      key: 'order',
      title: '排序',
      align: 'left',
      sorter: true
    },
    {
      key: 'createdBy',
      title: '创建人',
      align: 'left'
    },
    {
      key: 'updatedBy',
      title: '更新人',
      align: 'left'
    },
    {
      key: 'status',
      title: '状态',
      align: 'center',
      render: row => <SwitchStatus v-model:value={row.status} apiFn={status => skuApi.save({ ...row, status })} />
    },
    {
      key: 'operate',
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 150,
      render: row => (
        <div class="flex flex-center gap-12px">
          <NButton type="warning" text size="small" onClick={() => print(row.id)}>
            打印
          </NButton>
          <NButton type="primary" text size="small" onClick={() => edit(row.id)}>
            编辑
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
            {{
              default: () => '确定删除吗？',
              trigger: () => (
                <NButton type="error" text size="small">
                  删除
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  const { error } = await skuApi.batchDel(checkedRowKeys.value);

  if (!error) {
    onBatchDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleDelete(id: number) {
  const { error } = await skuApi.del(id);

  if (!error) {
    onDeleted(pagination, getDataByPage);
  } else {
    window.$message?.error(error.message);
  }
}

async function handleSort(sorter: DataTableSortState) {
  if (sorter.order) {
    updateSearchParams({
      _sort: sorter.columnKey as string,
      _order: sorter.order === 'ascend' ? 'asc' : 'desc'
    });
  } else {
    updateSearchParams({
      _sort: 'order,id',
      _order: 'desc,asc'
    });
  }
  await getData();
}

function edit(id: number) {
  handleEdit(id);
}

const itemId = ref(props.item?.id || 0);

async function handleItemChange(_: any, option?: any) {
  emitter.emit('showSku', option);
}

watch(
  () => itemId.value,
  async nval => {
    updateSearchParams({
      itemId: nval
    });
    await getData();
  }
);

// 打印
const printSettingVisible = ref(false);
const printData = ref({
  template: 'sku',
  id: 0
});

function print(id: number) {
  printData.value.id = id;
  printSettingVisible.value = true;
}
</script>

<template>
  <div class="h-full flex-col-stretch gap-12px overflow-hidden lt-sm:overflow-auto">
    <SearchBar v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard :bordered="false" size="small" class="sm:flex-1-hidden card-wrapper">
      <template #header>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        >
          <template #suffix>
            <RemoteSelect
              v-model:value="itemId"
              class="w-200px"
              label-field="name"
              value-field="id"
              :options="[item]"
              :api-fn="itemApi.list"
              placeholder="请选择物料"
              :clearable="false"
              @update:value="handleItemChange"
            />
            <NButton @click="emitter.emit('showSku', null)">
              <template #icon>
                <icon-ph-arrow-left />
              </template>
            </NButton>
          </template>
        </TableHeaderOperation>
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        striped
        :bordered="false"
        :row-key="row => row.id"
        :pagination="pagination"
        class="b-t-1px sm:h-full b-auto"
        @update:sorter="handleSort"
      />
      <OperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        :item="item"
        @submitted="getDataByPage"
      />
      <PrintSetting v-model:visible="printSettingVisible" :data="printData" />
    </NCard>
  </div>
</template>

<style scoped></style>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import axios from 'axios';
import dayjs from 'dayjs';
import { weatherIcon } from '@/constants/weather';

defineOptions({
  name: 'CityWeather'
});

const key = import.meta.env.VITE_AMAP_SERVICE_KEY;

const city = ref();
const today = ref();
const casts = ref();

async function getWeather() {
  const cache = localStorage.getItem('weather-cache');
  if (cache) {
    const { data, expires } = JSON.parse(cache);
    if (expires > Date.now()) {
      city.value = data.city;
      today.value = data.today;
      casts.value = data.casts;
      return;
    }
  }
  // 获取城市
  const res = await axios.request({
    url: 'https://restapi.amap.com/v3/ip',
    params: { key }
  });
  if (res.status === 200) {
    city.value = res.data.area || res.data.city;
    // 今天
    const todayRes = await axios.request({
      url: 'https://restapi.amap.com/v3/weather/weatherInfo',
      params: {
        key,
        city: city.value,
        extensions: 'base'
      }
    });
    today.value = todayRes.data.lives[0];

    // 未来4天
    const castsRes = await axios.request({
      url: 'https://restapi.amap.com/v3/weather/weatherInfo',
      params: {
        key,
        city: city.value,
        extensions: 'all'
      }
    });
    casts.value = castsRes.data.forecasts[0].casts;
    casts.value.shift();

    // 缓存
    localStorage.setItem(
      'weather-cache',
      JSON.stringify({
        data: {
          city: city.value,
          today: today.value,
          casts: casts.value
        },
        expires: Date.now() + 60 * 60 * 1000
      })
    );
  }
}

onMounted(async () => {
  await getWeather();
});
</script>

<template>
  <NCard v-if="today" :bordered="false" class="card-wrapper">
    <template #header>
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <SvgIcon icon="material-symbols-light:location-on-outline" class="text-24px text-primary"></SvgIcon>
          <span class="ml-10px">今日天气</span>
        </div>
        <div class="flex items-center">
          <NTag type="primary">{{ today.province }}</NTag>
          <icon-ph-caret-right class="mx-6px text-14px text-gray" />
          <NTag type="primary">{{ today.city }}</NTag>
        </div>
      </div>
    </template>
    <div class="flex items-center justify-between px-20px">
      <div class="flex items-center justify-center">
        <div class="temp text-36px">{{ today.temperature }}</div>
        <img class="ml-30px h-64px w-64px" :src="`./weather/${weatherIcon[today.weather]}.svg`" :alt="today.weather" />
        <div class="ml-16px text-16px">{{ today.weather }}</div>
      </div>
      <div class="flex flex-col items-center justify-center gap-y-4px">
        <div class="flex items-center">
          <SvgIcon icon="hugeicons:fast-wind" class="text-18px text-blue"></SvgIcon>
          <span class="ml-10px">{{ today.winddirection }}风 {{ today.windpower }}级</span>
        </div>
        <div class="flex items-center">
          <SvgIcon icon="material-symbols-light:humidity-percentage-outline" class="text-18px text-orange"></SvgIcon>
          <span class="ml-10px">湿度 {{ today.humidity }}%</span>
        </div>
      </div>
    </div>
    <NDivider />
    <div class="list">
      <NCarousel class="h-68px" direction="vertical" autoplay :show-dots="false">
        <div
          v-for="(item, index) in casts"
          :key="index"
          class="item flex items-center justify-between p-8px hover:bg-primary-50"
        >
          <div class="text-center">
            <div class="day">{{ ['明天', '后天', '大后天'][index] }}</div>
            <span class="text-12px text-gray">{{ dayjs(item.date).format('MM-DD') }}</span>
          </div>
          <div class="flex items-center">
            <img class="h-48px w-48px" :src="`./weather/${weatherIcon[item.dayweather]}.svg`" :alt="item.dayweather" />
            <span class="ml-4px">{{ item.dayweather }}</span>
          </div>
          <div class="flex items-center">
            <div class="temp">{{ item.nighttemp }}</div>
            <span class="ml-12px mr-6px text-12px text-gray">~</span>
            <div class="temp">{{ item.daytemp }}</div>
          </div>
          <div class="flex items-center">
            <div class="wind">{{ item.daywind }}风</div>
            <div class="ml-4px text-gray">({{ item.daypower }}级)</div>
          </div>
        </div>
      </NCarousel>
    </div>
  </NCard>
</template>

<style lang="scss" scoped>
.temp {
  position: relative;
  &:before {
    position: absolute;
    right: -20px;
    top: 0;
    content: '°';
  }
}
.list {
  .temp {
    &:before {
      right: -10px;
    }
  }
}
</style>

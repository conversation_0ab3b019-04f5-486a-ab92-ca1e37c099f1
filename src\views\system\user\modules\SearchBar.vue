<script setup lang="ts">
import { tenantApi } from '@/service/api/system';
import { useNaiveForm } from '@/hooks/common/form';
import { useDict } from '@/hooks/business/dict';

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const model = defineModel<Api.System.UserSearchParams>('model', { required: true });

async function reset() {
  await restoreValidation();
  emit('reset');
  emit('search');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem title="高级搜索" name="search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="100">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="用户名" path="username">
              <NInput v-model:value="model.username" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="昵称" path="nickname">
              <NInput v-model:value="model.nickname" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="手机号" path="phone">
              <NInput v-model:value="model.phone" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="邮箱" path="email">
              <NInput v-model:value="model.email" clearable />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="所属租户" path="tenantId">
              <RemoteSelect
                v-model:value="model.tenantId"
                label-field="name"
                value-field="id"
                :api-fn="tenantApi.list"
                placeholder="请选择所属租户"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="状态" path="status">
              <SelectBool v-model:value="model.status" :options="useDict().items('Status')" clearable />
            </NFormItemGi>
            <NFormItemGi suffix span="24 m:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="search">
                  <template #icon>
                    <icon-ph-magnifying-glass class="text-icon" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="reset">
                  <template #icon>
                    <icon-ph-arrow-counter-clockwise class="text-icon" />
                  </template>
                  重置
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>

import { request } from '@/service/request';

export const checkSkuApi = {
  // 获取盘点规格列表
  list: (params: Api.Wms.CheckSkuSearchParams) =>
    request<Api.Wms.CheckSkuList>({
      url: '/wms/check-skus',
      params
    }),

  // 创建盘点规格
  add: (data: Api.Wms.CheckSkuCreateParams) =>
    request<null>({
      url: '/wms/check-skus',
      method: 'POST',
      data
    }),

  // 更新盘点规格
  save: (data: Api.Wms.CheckSkuUpdateParams) =>
    request<null>({
      url: `/wms/check-skus/${data.id}`,
      method: 'PATCH',
      data
    }),

  // 删除盘点规格
  del: (id: number) =>
    request<null>({
      url: `/wms/check-skus/${id}`,
      method: 'DELETE'
    }),

  // 批量删除盘点规格
  batchDel: (ids: number[]) =>
    request<null>({
      url: '/wms/check-skus',
      method: 'DELETE',
      data: { ids }
    })
};
